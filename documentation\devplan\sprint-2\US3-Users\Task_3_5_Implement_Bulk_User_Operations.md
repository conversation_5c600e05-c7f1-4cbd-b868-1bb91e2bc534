# Task 3.5: Implement Bulk User Operations

## 📋 Task Overview
**User Story:** HU-2.3 - User Management System  
**Task ID:** 3.5  
**Estimated Time:** 3 hours  
**Priority:** High  
**Complexity:** High  

## 🎯 Description
Create bulk operations for user management that handles bulk user creation from CSV imports, bulk user updates with validation, bulk user activation/deactivation, error handling with detailed reporting, and progress tracking for large operations.

## 📦 Deliverables
- [ ] Bulk user creation from CSV imports
- [ ] Bulk user updates with validation
- [ ] Bulk user activation/deactivation
- [ ] Error handling with detailed reporting
- [ ] Progress tracking for large operations

## ✅ Acceptance Criteria
- [ ] bulk_create_users handles CSV import with validation
- [ ] bulk_update_users supports partial updates for multiple users
- [ ] bulk_activate/deactivate_users for status changes
- [ ] Detailed error reporting for failed operations
- [ ] Transaction safety with rollback on critical errors
- [ ] Progress tracking for operations >100 users
- [ ] Rate limiting to prevent system overload
- [ ] Audit logging for all bulk operations

## 🔧 Technical Requirements

### Bulk Operation Types
1. **Bulk Creation**
   - CSV file parsing and validation
   - Email uniqueness checking
   - Password generation or validation
   - Role assignment validation

2. **Bulk Updates**
   - Partial field updates
   - Role changes with permission validation
   - Status updates
   - Profile information updates

3. **Bulk Status Changes**
   - Activation/deactivation
   - Role assignments
   - Bulk deletion (soft delete)

### Performance Requirements
- Handle up to 1000 users per operation
- Background processing for large operations
- Progress reporting every 10% completion
- Memory-efficient processing

## 🔗 Dependencies
- **Prerequisite:** User model, UserService, Authentication system
- **File Processing:** CSV parsing libraries
- **Background Tasks:** Celery or FastAPI BackgroundTasks
- **Validation:** Comprehensive data validation

## 📝 Code Example
```python
from sqlalchemy.orm import Session
from fastapi import HTTPException, status, BackgroundTasks
from typing import List, Dict, Any, Optional, Union
import csv
import io
from datetime import datetime
import uuid
from dataclasses import dataclass

from ..models.user import User
from ..schemas.user import UserCreate, UserUpdate, BulkUserCreate, BulkUserUpdate
from ..services.auth_service import AuthService
from ..core.tenant_context import tenant_context
from ..core.security import get_password_hash

@dataclass
class BulkOperationResult:
    operation_id: str
    total: int
    successful: int
    failed: int
    errors: List[Dict[str, Any]]
    warnings: List[Dict[str, Any]]
    completed_at: Optional[datetime] = None
    status: str = "in_progress"

class BulkUserService:
    def __init__(self, db: Session):
        self.db = db
        self.auth_service = AuthService(db)
        self.max_batch_size = 1000
        self.chunk_size = 100

    def bulk_create_users_from_csv(
        self,
        csv_content: str,
        tenant_id: str,
        created_by_user_id: str,
        send_invitations: bool = True,
        background_tasks: Optional[BackgroundTasks] = None
    ) -> BulkOperationResult:
        """Create users from CSV content"""
        
        # Parse CSV content
        users_data = self._parse_csv_content(csv_content)
        
        if len(users_data) > self.max_batch_size:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot process more than {self.max_batch_size} users at once"
            )
        
        # Create operation tracking
        operation_id = str(uuid.uuid4())
        
        if len(users_data) > self.chunk_size and background_tasks:
            # Process in background for large operations
            background_tasks.add_task(
                self._process_bulk_creation_async,
                operation_id,
                users_data,
                tenant_id,
                created_by_user_id,
                send_invitations
            )
            
            return BulkOperationResult(
                operation_id=operation_id,
                total=len(users_data),
                successful=0,
                failed=0,
                errors=[],
                warnings=[],
                status="processing"
            )
        else:
            # Process immediately for small operations
            return self._process_bulk_creation(
                operation_id,
                users_data,
                tenant_id,
                created_by_user_id,
                send_invitations
            )

    def _parse_csv_content(self, csv_content: str) -> List[Dict[str, Any]]:
        """Parse CSV content and validate structure"""
        try:
            csv_file = io.StringIO(csv_content)
            reader = csv.DictReader(csv_file)
            
            required_fields = ['email']
            optional_fields = ['first_name', 'last_name', 'username', 'role', 'password']
            
            users_data = []
            for row_num, row in enumerate(reader, start=2):  # Start at 2 for header
                # Validate required fields
                if not row.get('email'):
                    raise ValueError(f"Row {row_num}: Email is required")
                
                # Clean and validate data
                user_data = {
                    'email': row['email'].strip().lower(),
                    'first_name': row.get('first_name', '').strip(),
                    'last_name': row.get('last_name', '').strip(),
                    'username': row.get('username', '').strip() or None,
                    'role': row.get('role', 'user').strip().lower(),
                    'password': row.get('password', '').strip(),
                    'row_number': row_num
                }
                
                # Generate password if not provided
                if not user_data['password']:
                    user_data['password'] = self._generate_random_password()
                
                users_data.append(user_data)
            
            return users_data
            
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"CSV parsing error: {str(e)}"
            )

    def _process_bulk_creation(
        self,
        operation_id: str,
        users_data: List[Dict[str, Any]],
        tenant_id: str,
        created_by_user_id: str,
        send_invitations: bool
    ) -> BulkOperationResult:
        """Process bulk user creation"""
        
        result = BulkOperationResult(
            operation_id=operation_id,
            total=len(users_data),
            successful=0,
            failed=0,
            errors=[],
            warnings=[]
        )
        
        with tenant_context(self.db, tenant_id):
            # Pre-validate emails for uniqueness
            existing_emails = self._check_existing_emails(
                [user['email'] for user in users_data],
                tenant_id
            )
            
            for user_data in users_data:
                try:
                    # Check if email already exists
                    if user_data['email'] in existing_emails:
                        result.errors.append({
                            'row': user_data['row_number'],
                            'email': user_data['email'],
                            'error': 'Email already exists'
                        })
                        result.failed += 1
                        continue
                    
                    # Create user
                    user_create = UserCreate(
                        email=user_data['email'],
                        password=user_data['password'],
                        first_name=user_data['first_name'],
                        last_name=user_data['last_name'],
                        username=user_data['username'],
                        role=user_data['role'],
                        tenant_id=tenant_id
                    )
                    
                    user = self.auth_service.create_user(user_create)
                    
                    # Add to existing emails to prevent duplicates in same batch
                    existing_emails.add(user_data['email'])
                    
                    result.successful += 1
                    
                    # Send invitation if requested
                    if send_invitations:
                        try:
                            self._send_welcome_email(user, user_data['password'])
                        except Exception as e:
                            result.warnings.append({
                                'row': user_data['row_number'],
                                'email': user_data['email'],
                                'warning': f'User created but email failed: {str(e)}'
                            })
                
                except Exception as e:
                    result.errors.append({
                        'row': user_data['row_number'],
                        'email': user_data['email'],
                        'error': str(e)
                    })
                    result.failed += 1
        
        result.completed_at = datetime.utcnow()
        result.status = "completed"
        
        return result

    def bulk_update_users(
        self,
        user_ids: List[str],
        updates: UserUpdate,
        tenant_id: str,
        updated_by_user_id: str
    ) -> BulkOperationResult:
        """Bulk update multiple users"""
        
        operation_id = str(uuid.uuid4())
        result = BulkOperationResult(
            operation_id=operation_id,
            total=len(user_ids),
            successful=0,
            failed=0,
            errors=[],
            warnings=[]
        )
        
        with tenant_context(self.db, tenant_id):
            # Get all users to update
            users = self.db.query(User).filter(
                User.id.in_(user_ids),
                User.tenant_id == tenant_id,
                User.is_deleted == False
            ).all()
            
            found_user_ids = {user.id for user in users}
            missing_user_ids = set(user_ids) - found_user_ids
            
            # Report missing users
            for missing_id in missing_user_ids:
                result.errors.append({
                    'user_id': missing_id,
                    'error': 'User not found'
                })
                result.failed += 1
            
            # Update found users
            update_data = updates.dict(exclude_unset=True)
            
            for user in users:
                try:
                    # Apply updates
                    for field, value in update_data.items():
                        if hasattr(user, field):
                            setattr(user, field, value)
                    
                    user.updated_at = datetime.utcnow()
                    result.successful += 1
                    
                except Exception as e:
                    result.errors.append({
                        'user_id': user.id,
                        'email': user.email,
                        'error': str(e)
                    })
                    result.failed += 1
            
            try:
                self.db.commit()
            except Exception as e:
                self.db.rollback()
                # Mark all as failed if commit fails
                result.failed = result.total
                result.successful = 0
                result.errors = [{'error': f'Database commit failed: {str(e)}'}]
        
        result.completed_at = datetime.utcnow()
        result.status = "completed"
        
        return result

    def bulk_activate_users(
        self,
        user_ids: List[str],
        tenant_id: str,
        activated_by_user_id: str
    ) -> BulkOperationResult:
        """Bulk activate users"""
        updates = UserUpdate(is_active=True)
        return self.bulk_update_users(user_ids, updates, tenant_id, activated_by_user_id)

    def bulk_deactivate_users(
        self,
        user_ids: List[str],
        tenant_id: str,
        deactivated_by_user_id: str
    ) -> BulkOperationResult:
        """Bulk deactivate users"""
        updates = UserUpdate(is_active=False)
        return self.bulk_update_users(user_ids, updates, tenant_id, deactivated_by_user_id)

    def bulk_delete_users(
        self,
        user_ids: List[str],
        tenant_id: str,
        deleted_by_user_id: str
    ) -> BulkOperationResult:
        """Bulk soft delete users"""
        
        operation_id = str(uuid.uuid4())
        result = BulkOperationResult(
            operation_id=operation_id,
            total=len(user_ids),
            successful=0,
            failed=0,
            errors=[],
            warnings=[]
        )
        
        with tenant_context(self.db, tenant_id):
            users = self.db.query(User).filter(
                User.id.in_(user_ids),
                User.tenant_id == tenant_id,
                User.is_deleted == False
            ).all()
            
            for user in users:
                try:
                    # Prevent self-deletion
                    if user.id == deleted_by_user_id:
                        result.errors.append({
                            'user_id': user.id,
                            'email': user.email,
                            'error': 'Cannot delete yourself'
                        })
                        result.failed += 1
                        continue
                    
                    user.soft_delete()
                    result.successful += 1
                    
                except Exception as e:
                    result.errors.append({
                        'user_id': user.id,
                        'email': user.email,
                        'error': str(e)
                    })
                    result.failed += 1
            
            self.db.commit()
        
        result.completed_at = datetime.utcnow()
        result.status = "completed"
        
        return result

    def _check_existing_emails(self, emails: List[str], tenant_id: str) -> set:
        """Check which emails already exist in the tenant"""
        existing = self.db.query(User.email).filter(
            User.email.in_(emails),
            User.tenant_id == tenant_id,
            User.is_deleted == False
        ).all()
        
        return {email[0] for email in existing}

    def _generate_random_password(self) -> str:
        """Generate a secure random password"""
        import secrets
        import string
        
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        password = ''.join(secrets.choice(alphabet) for _ in range(12))
        return password

    def _send_welcome_email(self, user: User, password: str):
        """Send welcome email with credentials"""
        # Implementation would depend on email service
        # This is a placeholder
        pass

    async def _process_bulk_creation_async(
        self,
        operation_id: str,
        users_data: List[Dict[str, Any]],
        tenant_id: str,
        created_by_user_id: str,
        send_invitations: bool
    ):
        """Process bulk creation asynchronously"""
        # This would be implemented with Celery or similar
        # for true background processing
        return self._process_bulk_creation(
            operation_id,
            users_data,
            tenant_id,
            created_by_user_id,
            send_invitations
        )

    def get_operation_status(self, operation_id: str) -> Optional[BulkOperationResult]:
        """Get status of a bulk operation"""
        # This would typically be stored in Redis or database
        # For now, return None (operation not found)
        return None

# CSV template generation
def generate_csv_template() -> str:
    """Generate CSV template for bulk user import"""
    template = """email,first_name,last_name,username,role,password
<EMAIL>,John,Doe,johndoe,user,
<EMAIL>,Jane,Smith,janesmith,admin,custompassword123
<EMAIL>,Bob,Johnson,,user,"""
    
    return template
```

## 🎯 Definition of Done
- [ ] Bulk user operations service is implemented
- [ ] CSV import functionality works correctly
- [ ] Bulk updates handle partial field updates
- [ ] Error handling provides detailed reporting
- [ ] Transaction safety prevents partial failures
- [ ] Progress tracking works for large operations
- [ ] Performance requirements are met
- [ ] Audit logging captures all operations
- [ ] Code review is completed
