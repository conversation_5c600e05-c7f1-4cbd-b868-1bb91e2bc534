# Task 3.4: Implement User Search and Filtering

## 📋 Task Overview
**User Story:** HU-2.3 - User Management System  
**Task ID:** 3.4  
**Estimated Time:** 3 hours  
**Priority:** High  
**Complexity:** Medium  

## 🎯 Description
Create advanced search and filtering capabilities for user management that provides search functionality with multiple criteria, filtering by role/status/activity, pagination with configurable page sizes, sorting by various user attributes, and performance optimization for large user lists.

## 📦 Deliverables
- [ ] Search functionality with multiple criteria
- [ ] Filtering by role, status, activity, and custom fields
- [ ] Pagination with configurable page sizes
- [ ] Sorting by various user attributes
- [ ] Performance optimization for large user lists

## ✅ Acceptance Criteria
- [ ] Search by name, email, username with partial matching
- [ ] Filter by role (owner, admin, user)
- [ ] Filter by status (active, inactive, pending)
- [ ] Filter by last login date range
- [ ] Sort by name, email, created_at, last_login
- [ ] Pagination with skip/limit parameters
- [ ] Search performance optimized with database indexes
- [ ] Results include total count for pagination

## 🔧 Technical Requirements

### Search Capabilities
1. **Text Search**
   - Full-text search across name, email, username
   - Partial matching with wildcards
   - Case-insensitive search
   - Search result highlighting

2. **Filtering Options**
   - Role-based filtering
   - Status filtering (active/inactive)
   - Date range filtering
   - Custom field filtering

3. **Sorting and Pagination**
   - Multiple sort fields
   - Ascending/descending order
   - Configurable page sizes
   - Efficient pagination with cursors

### Performance Requirements
- Search response time <300ms
- Support for 10,000+ users per tenant
- Efficient database queries
- Proper indexing strategy

## 🔗 Dependencies
- **Prerequisite:** User model, User schemas, UserService base
- **Database:** Optimized indexes for search fields
- **Performance:** Query optimization and caching

## 📝 Code Example
```python
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, text, desc, asc
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date
from enum import Enum

from ..models.user import User
from ..schemas.user import UserSearch, UserInDB
from ..core.tenant_context import tenant_context

class SortOrder(str, Enum):
    ASC = "asc"
    DESC = "desc"

class UserSearchService:
    def __init__(self, db: Session):
        self.db = db

    def search_users(
        self,
        tenant_id: str,
        search_params: UserSearch,
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """Advanced user search with filtering and pagination"""
        
        with tenant_context(self.db, tenant_id):
            # Build base query
            query = self._build_base_query(tenant_id)
            
            # Apply search filters
            query = self._apply_text_search(query, search_params.search_term)
            query = self._apply_role_filter(query, search_params.role)
            query = self._apply_status_filter(query, search_params.is_active)
            query = self._apply_date_filters(query, search_params)
            
            # Get total count before pagination
            total_count = query.count()
            
            # Apply sorting
            query = self._apply_sorting(query, search_params.sort_by, search_params.sort_order)
            
            # Apply pagination
            users = query.offset(skip).limit(limit).all()
            
            # Calculate pagination info
            page = (skip // limit) + 1
            total_pages = (total_count + limit - 1) // limit
            
            return {
                "users": users,
                "total_count": total_count,
                "page": page,
                "pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1,
                "per_page": limit
            }

    def _build_base_query(self, tenant_id: str):
        """Build base query with tenant filtering"""
        return self.db.query(User).filter(
            User.tenant_id == tenant_id,
            User.is_deleted == False
        )

    def _apply_text_search(self, query, search_term: Optional[str]):
        """Apply text search across multiple fields"""
        if not search_term:
            return query
        
        search_term = f"%{search_term.strip()}%"
        
        return query.filter(
            or_(
                User.first_name.ilike(search_term),
                User.last_name.ilike(search_term),
                User.email.ilike(search_term),
                User.username.ilike(search_term),
                func.concat(User.first_name, ' ', User.last_name).ilike(search_term)
            )
        )

    def _apply_role_filter(self, query, role: Optional[str]):
        """Apply role-based filtering"""
        if role:
            return query.filter(User.role == role)
        return query

    def _apply_status_filter(self, query, is_active: Optional[bool]):
        """Apply status filtering"""
        if is_active is not None:
            return query.filter(User.is_active == is_active)
        return query

    def _apply_date_filters(self, query, search_params: UserSearch):
        """Apply date range filters"""
        # Created date filters
        if search_params.created_after:
            query = query.filter(User.created_at >= search_params.created_after)
        
        if search_params.created_before:
            query = query.filter(User.created_at <= search_params.created_before)
        
        # Last login filters
        if search_params.last_login_after:
            query = query.filter(User.last_login >= search_params.last_login_after)
        
        if search_params.last_login_before:
            query = query.filter(User.last_login <= search_params.last_login_before)
        
        return query

    def _apply_sorting(self, query, sort_by: Optional[str], sort_order: Optional[str]):
        """Apply sorting to query"""
        if not sort_by:
            sort_by = "created_at"
        
        if not sort_order:
            sort_order = SortOrder.DESC
        
        # Map sort fields to model attributes
        sort_fields = {
            "created_at": User.created_at,
            "updated_at": User.updated_at,
            "last_login": User.last_login,
            "email": User.email,
            "first_name": User.first_name,
            "last_name": User.last_name,
            "username": User.username,
            "login_count": User.login_count,
            "role": User.role
        }
        
        sort_field = sort_fields.get(sort_by, User.created_at)
        
        if sort_order == SortOrder.DESC:
            return query.order_by(desc(sort_field))
        else:
            return query.order_by(asc(sort_field))

    def get_search_suggestions(self, tenant_id: str, query_text: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get search suggestions for autocomplete"""
        if not query_text or len(query_text) < 2:
            return []
        
        with tenant_context(self.db, tenant_id):
            search_term = f"{query_text}%"
            
            # Search for email suggestions
            email_suggestions = self.db.query(User.email).filter(
                User.tenant_id == tenant_id,
                User.is_deleted == False,
                User.email.ilike(search_term)
            ).limit(limit // 2).all()
            
            # Search for name suggestions
            name_suggestions = self.db.query(
                func.concat(User.first_name, ' ', User.last_name).label('full_name')
            ).filter(
                User.tenant_id == tenant_id,
                User.is_deleted == False,
                or_(
                    User.first_name.ilike(search_term),
                    User.last_name.ilike(search_term)
                )
            ).limit(limit // 2).all()
            
            suggestions = []
            
            # Add email suggestions
            for email in email_suggestions:
                suggestions.append({
                    "type": "email",
                    "value": email[0],
                    "label": email[0]
                })
            
            # Add name suggestions
            for name in name_suggestions:
                suggestions.append({
                    "type": "name",
                    "value": name[0],
                    "label": name[0]
                })
            
            return suggestions[:limit]

    def get_filter_options(self, tenant_id: str) -> Dict[str, Any]:
        """Get available filter options for the tenant"""
        with tenant_context(self.db, tenant_id):
            # Get available roles
            roles = self.db.query(User.role).filter(
                User.tenant_id == tenant_id,
                User.is_deleted == False
            ).distinct().all()
            
            # Get user count by status
            status_counts = self.db.query(
                User.is_active,
                func.count(User.id).label('count')
            ).filter(
                User.tenant_id == tenant_id,
                User.is_deleted == False
            ).group_by(User.is_active).all()
            
            # Get date ranges
            date_range = self.db.query(
                func.min(User.created_at).label('earliest'),
                func.max(User.created_at).label('latest')
            ).filter(
                User.tenant_id == tenant_id,
                User.is_deleted == False
            ).first()
            
            return {
                "roles": [role[0] for role in roles],
                "status_counts": {
                    "active": next((sc.count for sc in status_counts if sc.is_active), 0),
                    "inactive": next((sc.count for sc in status_counts if not sc.is_active), 0)
                },
                "date_range": {
                    "earliest": date_range.earliest.isoformat() if date_range.earliest else None,
                    "latest": date_range.latest.isoformat() if date_range.latest else None
                }
            }

    def export_search_results(
        self,
        tenant_id: str,
        search_params: UserSearch,
        format: str = "csv"
    ) -> str:
        """Export search results to file"""
        with tenant_context(self.db, tenant_id):
            # Build query without pagination
            query = self._build_base_query(tenant_id)
            query = self._apply_text_search(query, search_params.search_term)
            query = self._apply_role_filter(query, search_params.role)
            query = self._apply_status_filter(query, search_params.is_active)
            query = self._apply_date_filters(query, search_params)
            query = self._apply_sorting(query, search_params.sort_by, search_params.sort_order)
            
            users = query.all()
            
            if format.lower() == "csv":
                return self._export_to_csv(users)
            elif format.lower() == "json":
                return self._export_to_json(users)
            else:
                raise ValueError(f"Unsupported export format: {format}")

    def _export_to_csv(self, users: List[User]) -> str:
        """Export users to CSV format"""
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write header
        writer.writerow([
            'Email', 'First Name', 'Last Name', 'Username', 'Role',
            'Active', 'Created At', 'Last Login', 'Login Count'
        ])
        
        # Write data
        for user in users:
            writer.writerow([
                user.email,
                user.first_name or '',
                user.last_name or '',
                user.username or '',
                user.role,
                'Yes' if user.is_active else 'No',
                user.created_at.isoformat() if user.created_at else '',
                user.last_login.isoformat() if user.last_login else '',
                user.login_count
            ])
        
        return output.getvalue()

    def _export_to_json(self, users: List[User]) -> str:
        """Export users to JSON format"""
        import json
        
        user_data = []
        for user in users:
            user_data.append({
                'email': user.email,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'username': user.username,
                'role': user.role,
                'is_active': user.is_active,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'login_count': user.login_count
            })
        
        return json.dumps(user_data, indent=2)

# Database indexes for optimal search performance
def create_user_search_indexes(db: Session):
    """Create indexes for optimal user search performance"""
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_users_search_text ON users USING gin(to_tsvector('english', coalesce(first_name, '') || ' ' || coalesce(last_name, '') || ' ' || coalesce(email, '') || ' ' || coalesce(username, '')))",
        "CREATE INDEX IF NOT EXISTS idx_users_email_trgm ON users USING gin(email gin_trgm_ops)",
        "CREATE INDEX IF NOT EXISTS idx_users_name_trgm ON users USING gin((first_name || ' ' || last_name) gin_trgm_ops)",
        "CREATE INDEX IF NOT EXISTS idx_users_tenant_role ON users(tenant_id, role)",
        "CREATE INDEX IF NOT EXISTS idx_users_tenant_status ON users(tenant_id, is_active, is_deleted)",
        "CREATE INDEX IF NOT EXISTS idx_users_tenant_created ON users(tenant_id, created_at)",
        "CREATE INDEX IF NOT EXISTS idx_users_tenant_login ON users(tenant_id, last_login)",
    ]
    
    # Enable required extensions
    extensions = [
        "CREATE EXTENSION IF NOT EXISTS pg_trgm",
        "CREATE EXTENSION IF NOT EXISTS unaccent"
    ]
    
    for ext_sql in extensions:
        try:
            db.execute(text(ext_sql))
            db.commit()
        except Exception as e:
            print(f"Extension creation failed: {e}")
            db.rollback()
    
    for index_sql in indexes:
        try:
            db.execute(text(index_sql))
            db.commit()
        except Exception as e:
            print(f"Index creation failed: {e}")
            db.rollback()
```

## 🎯 Definition of Done
- [ ] User search service is implemented
- [ ] Text search works across multiple fields
- [ ] Filtering by role, status, and dates works
- [ ] Sorting and pagination are functional
- [ ] Search performance meets requirements (<300ms)
- [ ] Database indexes are optimized
- [ ] Export functionality works
- [ ] Search suggestions provide autocomplete
- [ ] Code review is completed
