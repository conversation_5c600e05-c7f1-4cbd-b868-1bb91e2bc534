# Task 2.2: Implement Tenant Service

## 📋 Task Overview
**User Story:** HU-2.2 - Tenant CRUD Operations  
**Task ID:** 2.2  
**Estimated Time:** 4 hours  
**Priority:** Critical  
**Complexity:** High  

## 🎯 Description
Create a TenantService class that handles all tenant management operations including creation with automatic owner user, CRUD operations with validation, tenant statistics, and soft deletion functionality.

## 📦 Deliverables
- [ ] Create `backend/src/services/tenant_service.py`
- [ ] Tenant creation with automatic owner user
- [ ] Tenant CRUD operations with validation
- [ ] Tenant statistics and analytics
- [ ] Tenant soft deletion functionality

## ✅ Acceptance Criteria
- [ ] create_tenant validates slug and domain uniqueness
- [ ] Automatically creates owner user during tenant creation
- [ ] get_tenant_by_id and get_tenant_by_slug methods
- [ ] get_tenants_with_stats includes user count aggregation
- [ ] update_tenant supports partial updates
- [ ] delete_tenant implements soft deletion
- [ ] Proper error handling for all operations
- [ ] Transaction management for tenant+owner creation

## 🔧 Technical Requirements

### Core Service Methods
1. **create_tenant(tenant_data: TenantCreate) -> Tenant**
   - Validate slug and domain uniqueness
   - Create tenant and owner user in single transaction
   - Set up default tenant settings

2. **get_tenant_by_id(tenant_id: str) -> Optional[Tenant]**
   - Retrieve tenant with soft deletion check
   - Include related data if needed

3. **get_tenants_with_stats(skip: int, limit: int) -> List[TenantWithStats]**
   - Aggregate user statistics
   - Optimize query performance

4. **update_tenant(tenant_id: str, tenant_data: TenantUpdate) -> Tenant**
   - Partial updates with validation
   - Handle settings and branding updates

5. **delete_tenant(tenant_id: str) -> bool**
   - Soft deletion implementation
   - Cascade considerations

## 🔗 Dependencies
- **Prerequisite:** Tenant schemas (Task 2.1), User model and AuthService
- **Database:** Tenant and User tables with proper relationships
- **Services:** AuthService for owner user creation

## 🧪 Testing Requirements
- [ ] Test tenant creation with owner user
- [ ] Test slug and domain uniqueness validation
- [ ] Test tenant statistics calculation
- [ ] Test tenant update operations
- [ ] Test tenant soft deletion
- [ ] Test error handling for conflicts
- [ ] Test transaction rollback scenarios
- [ ] Performance tests for statistics queries

## 📝 Code Example
```python
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import HTTPException, status
from typing import List, Optional

from ..models.tenant import Tenant
from ..models.user import User
from ..schemas.tenant import TenantCreate, TenantUpdate, TenantWithStats
from ..services.auth_service import AuthService
from ..schemas.auth import UserCreate
from ..core.tenant_context import tenant_context

class TenantService:
    def __init__(self, db: Session):
        self.db = db

    def create_tenant(self, tenant_data: TenantCreate) -> Tenant:
        """Create a new tenant with owner user"""
        # Check if slug already exists
        existing_tenant = self.db.query(Tenant).filter(
            Tenant.slug == tenant_data.slug
        ).first()

        if existing_tenant:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Tenant slug already exists"
            )

        # Check if domain already exists (if provided)
        if tenant_data.domain:
            existing_domain = self.db.query(Tenant).filter(
                Tenant.domain == tenant_data.domain
            ).first()

            if existing_domain:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Domain already exists"
                )

        try:
            # Create tenant
            db_tenant = Tenant(
                name=tenant_data.name,
                slug=tenant_data.slug,
                domain=tenant_data.domain,
                contact_email=tenant_data.contact_email,
                contact_phone=tenant_data.contact_phone,
                address=tenant_data.address
            )

            self.db.add(db_tenant)
            self.db.flush()  # Get the ID without committing

            # Create owner user
            auth_service = AuthService(self.db)
            owner_data = UserCreate(
                email=tenant_data.owner_email,
                password=tenant_data.owner_password,
                first_name=tenant_data.owner_first_name,
                last_name=tenant_data.owner_last_name,
                role="owner",
                tenant_id=db_tenant.id
            )

            auth_service.create_user(owner_data)

            self.db.commit()
            self.db.refresh(db_tenant)

            return db_tenant

        except Exception as e:
            self.db.rollback()
            raise e

    def get_tenant_by_id(self, tenant_id: str) -> Optional[Tenant]:
        """Get tenant by ID"""
        return self.db.query(Tenant).filter(
            Tenant.id == tenant_id,
            Tenant.is_deleted == False
        ).first()

    def get_tenant_by_slug(self, slug: str) -> Optional[Tenant]:
        """Get tenant by slug"""
        return self.db.query(Tenant).filter(
            Tenant.slug == slug,
            Tenant.is_deleted == False
        ).first()

    def get_tenants_with_stats(self, skip: int = 0, limit: int = 100) -> List[TenantWithStats]:
        """Get tenants with user statistics"""
        tenants = self.db.query(
            Tenant,
            func.count(User.id).label('user_count'),
            func.count(func.nullif(User.is_active, False)).label('active_user_count')
        ).outerjoin(User).filter(
            Tenant.is_deleted == False
        ).group_by(Tenant.id).offset(skip).limit(limit).all()

        result = []
        for tenant, user_count, active_user_count in tenants:
            tenant_dict = tenant.__dict__.copy()
            tenant_dict['user_count'] = user_count or 0
            tenant_dict['active_user_count'] = active_user_count or 0
            result.append(TenantWithStats(**tenant_dict))

        return result

    def update_tenant(self, tenant_id: str, tenant_data: TenantUpdate) -> Tenant:
        """Update tenant information"""
        tenant = self.get_tenant_by_id(tenant_id)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        update_data = tenant_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(tenant, field, value)

        self.db.commit()
        self.db.refresh(tenant)

        return tenant

    def delete_tenant(self, tenant_id: str) -> bool:
        """Soft delete tenant"""
        tenant = self.get_tenant_by_id(tenant_id)
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        tenant.soft_delete()
        self.db.commit()

        return True
```

## 🎯 Definition of Done
- [ ] TenantService class is fully implemented
- [ ] Tenant creation with owner user works in transaction
- [ ] All CRUD operations function correctly
- [ ] Statistics aggregation is optimized
- [ ] Error handling is comprehensive
- [ ] All tests pass with >95% coverage
- [ ] Performance requirements are met
- [ ] Code review is completed
