# Task 1.9: Update Environment Configuration

## 📋 Task Overview
**User Story:** HU-2.1 - JWT Authentication System  
**Task ID:** 1.9  
**Estimated Time:** 1 hour  
**Priority:** Critical  
**Complexity:** Low  

## 🎯 Description
Add required environment variables and configuration for authentication system including JWT settings, security configuration, validation for required settings, and documentation for deployment.

## 📦 Deliverables
- [ ] Update `backend/.env.example` with auth variables
- [ ] Update `backend/src/core/config.py` with auth settings
- [ ] Document configuration requirements
- [ ] Add validation for required settings

## ✅ Acceptance Criteria
- [ ] SECRET_KEY for JWT signing is configurable
- [ ] ACCESS_TOKEN_EXPIRE_MINUTES is configurable (default 1440)
- [ ] ALGORITHM is configurable (default HS256)
- [ ] Configuration validation prevents startup with missing values
- [ ] Documentation explains each configuration option
- [ ] Development and production configurations are documented

## 🔧 Technical Requirements

### Configuration Categories
1. **JWT Settings**
   - SECRET_KEY (required)
   - ALGORITHM (default HS256)
   - ACCESS_TOKEN_EXPIRE_MINUTES (default 1440)

2. **Security Settings**
   - ALLOWED_ORIGINS for CORS
   - ALLOWED_HOSTS for security
   - PASSWORD_MIN_LENGTH

3. **Database Settings**
   - Connection strings
   - Pool configuration

4. **Email Settings**
   - SMTP configuration
   - Email templates

## 🔗 Dependencies
- **Prerequisite:** Basic application configuration
- **Validation:** Pydantic settings validation
- **Security:** Environment variable security

## 📝 Code Example
```python
# backend/.env.example
# ======================
# Authentication Settings
# ======================

# JWT Configuration (REQUIRED)
SECRET_KEY=your-super-secret-jwt-key-here-minimum-32-characters-long
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# ======================
# Security Settings
# ======================

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000","http://localhost:3001","https://yourdomain.com"]
ALLOWED_HOSTS=["localhost","127.0.0.1","yourdomain.com"]

# Password Policy
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL=false

# ======================
# Database Settings
# ======================

# PostgreSQL Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/dbname
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# ======================
# Email Settings
# ======================

# SMTP Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false

# Email Settings
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Your Platform Name

# ======================
# Application Settings
# ======================

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=Multi-tenant Platform
VERSION=1.0.0

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# File Upload
MAX_FILE_SIZE=5242880  # 5MB
UPLOAD_PATH=./uploads

# backend/src/core/config.py
import secrets
from typing import List, Optional, Union
from pydantic import BaseSettings, validator, EmailStr
import os

class Settings(BaseSettings):
    # ======================
    # Authentication Settings
    # ======================
    
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 1440  # 24 hours
    
    @validator("SECRET_KEY")
    def validate_secret_key(cls, v):
        if not v:
            raise ValueError("SECRET_KEY is required")
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    # ======================
    # Security Settings
    # ======================
    
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]
    
    # Password Policy
    PASSWORD_MIN_LENGTH: int = 8
    PASSWORD_REQUIRE_UPPERCASE: bool = True
    PASSWORD_REQUIRE_LOWERCASE: bool = True
    PASSWORD_REQUIRE_NUMBERS: bool = True
    PASSWORD_REQUIRE_SPECIAL: bool = False
    
    @validator("ALLOWED_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("ALLOWED_HOSTS", pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(",")]
        return v
    
    # ======================
    # Database Settings
    # ======================
    
    DATABASE_URL: str
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    @validator("DATABASE_URL")
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("DATABASE_URL is required")
        if not v.startswith(("postgresql://", "postgresql+psycopg2://")):
            raise ValueError("DATABASE_URL must be a PostgreSQL connection string")
        return v
    
    # ======================
    # Email Settings
    # ======================
    
    SMTP_HOST: Optional[str] = None
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    SMTP_TLS: bool = True
    SMTP_SSL: bool = False
    
    EMAIL_FROM: Optional[EmailStr] = None
    EMAIL_FROM_NAME: Optional[str] = None
    
    @validator("EMAIL_FROM")
    def validate_email_from(cls, v, values):
        if values.get("SMTP_HOST") and not v:
            raise ValueError("EMAIL_FROM is required when SMTP is configured")
        return v
    
    # ======================
    # Application Settings
    # ======================
    
    ENVIRONMENT: str = "development"
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Multi-tenant Platform"
    VERSION: str = "1.0.0"
    
    # Rate Limiting
    RATE_LIMIT_PER_MINUTE: int = 60
    RATE_LIMIT_BURST: int = 10
    
    # File Upload
    MAX_FILE_SIZE: int = 5242880  # 5MB
    UPLOAD_PATH: str = "./uploads"
    
    @validator("ENVIRONMENT")
    def validate_environment(cls, v):
        allowed_envs = ["development", "staging", "production"]
        if v not in allowed_envs:
            raise ValueError(f"ENVIRONMENT must be one of: {allowed_envs}")
        return v
    
    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"LOG_LEVEL must be one of: {allowed_levels}")
        return v.upper()
    
    # ======================
    # Computed Properties
    # ======================
    
    @property
    def is_production(self) -> bool:
        return self.ENVIRONMENT == "production"
    
    @property
    def is_development(self) -> bool:
        return self.ENVIRONMENT == "development"
    
    @property
    def smtp_configured(self) -> bool:
        return all([
            self.SMTP_HOST,
            self.SMTP_USER,
            self.SMTP_PASSWORD,
            self.EMAIL_FROM
        ])
    
    # ======================
    # Configuration Validation
    # ======================
    
    def validate_production_config(self):
        """Validate configuration for production environment"""
        if self.is_production:
            errors = []
            
            # Check required production settings
            if self.DEBUG:
                errors.append("DEBUG must be False in production")
            
            if self.SECRET_KEY == "your-super-secret-jwt-key-here-minimum-32-characters-long":
                errors.append("SECRET_KEY must be changed from default value")
            
            if "localhost" in self.ALLOWED_ORIGINS:
                errors.append("ALLOWED_ORIGINS should not include localhost in production")
            
            if not self.smtp_configured:
                errors.append("SMTP must be configured in production")
            
            if errors:
                raise ValueError(f"Production configuration errors: {'; '.join(errors)}")
    
    def generate_secret_key(self) -> str:
        """Generate a secure secret key"""
        return secrets.token_urlsafe(32)
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Create settings instance
settings = Settings()

# Validate production configuration
if settings.is_production:
    settings.validate_production_config()

# backend/src/core/logging.py
import logging
import sys
from typing import Dict, Any
from .config import settings

def setup_logging():
    """Setup application logging"""
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, settings.LOG_LEVEL),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Configure specific loggers
    loggers = {
        "uvicorn": logging.INFO,
        "uvicorn.access": logging.INFO,
        "sqlalchemy.engine": logging.WARNING,
        "passlib": logging.WARNING,
    }
    
    for logger_name, level in loggers.items():
        logging.getLogger(logger_name).setLevel(level)
    
    # Security logger for authentication events
    security_logger = logging.getLogger("security")
    security_handler = logging.StreamHandler()
    security_formatter = logging.Formatter(
        "%(asctime)s - SECURITY - %(levelname)s - %(message)s"
    )
    security_handler.setFormatter(security_formatter)
    security_logger.addHandler(security_handler)
    security_logger.setLevel(logging.INFO)

# Configuration validation script
# backend/scripts/validate_config.py
#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.config import settings

def validate_configuration():
    """Validate application configuration"""
    print("Validating configuration...")
    
    try:
        # Test database connection
        from src.core.database import engine
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        print("✓ Database connection successful")
        
        # Test JWT configuration
        from src.core.security import create_access_token, verify_token
        test_data = {"sub": "test"}
        token = create_access_token(test_data)
        payload = verify_token(token)
        assert payload["sub"] == "test"
        print("✓ JWT configuration valid")
        
        # Test email configuration (if configured)
        if settings.smtp_configured:
            print("✓ SMTP configuration present")
        else:
            print("⚠ SMTP not configured (email features disabled)")
        
        # Production checks
        if settings.is_production:
            settings.validate_production_config()
            print("✓ Production configuration valid")
        
        print("\n✅ Configuration validation successful!")
        return True
        
    except Exception as e:
        print(f"\n❌ Configuration validation failed: {e}")
        return False

if __name__ == "__main__":
    success = validate_configuration()
    sys.exit(0 if success else 1)
```

## 🧪 Testing Requirements
- [ ] Test configuration loading from environment
- [ ] Test validation of required settings
- [ ] Test production configuration validation
- [ ] Test secret key generation
- [ ] Test CORS and security settings
- [ ] Test database connection validation
- [ ] Test email configuration validation

## 📊 Validation Checklist
- [ ] All required environment variables are documented
- [ ] Configuration validation prevents startup with invalid settings
- [ ] Production configuration is secure
- [ ] Development configuration is convenient
- [ ] Documentation is clear and complete
- [ ] Secret key generation is secure

## 🚨 Security Considerations
- [ ] Secret key is properly secured
- [ ] Production configuration prevents debug mode
- [ ] CORS origins are properly restricted
- [ ] Database credentials are protected
- [ ] Email credentials are secured
- [ ] No sensitive data in logs

## 📈 Performance Considerations
- [ ] Configuration loading is efficient
- [ ] Database pool settings are optimized
- [ ] Rate limiting is properly configured
- [ ] File upload limits prevent abuse

## 🎯 Definition of Done
- [ ] Environment configuration is complete
- [ ] All authentication settings are configurable
- [ ] Configuration validation works
- [ ] Documentation is comprehensive
- [ ] Production configuration is secure
- [ ] Development setup is streamlined
- [ ] Validation script works correctly
- [ ] Code review is completed
