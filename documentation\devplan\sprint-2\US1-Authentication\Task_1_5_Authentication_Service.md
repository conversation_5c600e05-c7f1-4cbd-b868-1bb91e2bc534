# Task 1.5: Implement Authentication Service

## 📋 Task Overview
**User Story:** HU-2.1 - JWT Authentication System  
**Task ID:** 1.5  
**Estimated Time:** 3 hours  
**Priority:** Critical  
**Complexity:** High  

## 🎯 Description
Create business logic for user authentication and management that handles the core authentication workflows including user login, token generation, and user creation with proper validation and security measures.

## 📦 Deliverables
- [ ] Create `backend/src/services/auth_service.py`
- [ ] User authentication with email/password
- [ ] JWT token creation for authenticated users
- [ ] User creation with proper validation
- [ ] Login tracking and statistics

## ✅ Acceptance Criteria
- [ ] authenticate_user validates credentials against database
- [ ] Supports both tenant-specific and owner login flows
- [ ] Password verification uses secure comparison
- [ ] Login tracking updates last_login and login_count
- [ ] create_access_token_for_user generates proper JWT
- [ ] create_user handles email uniqueness within tenant
- [ ] Proper error handling for all failure cases

## 🔧 Technical Requirements

### Service Methods
1. **authenticate_user(login_data: LoginRequest) -> Optional[User]**
   - Validate email and password
   - Support tenant-specific login
   - Support owner login without tenant
   - Update login tracking

2. **create_access_token_for_user(user: User) -> dict**
   - Generate JWT with proper claims
   - Include user information in response
   - Set appropriate expiration

3. **create_user(user_data: UserCreate) -> User**
   - Validate email uniqueness within tenant
   - Hash password securely
   - Create user with proper defaults

### Security Requirements
- Secure password comparison (timing attack protection)
- Proper error handling without information disclosure
- Login attempt tracking for security monitoring
- Tenant isolation enforcement

## 🔗 Dependencies
- **Prerequisite:** Security utilities (Task 1.2), User model (Task 1.1), Schemas (Task 1.4)
- **Database:** User and Tenant models available
- **Security:** Password hashing and JWT functions
- **Context:** Tenant context system for RLS

## 🧪 Testing Requirements
- [ ] Test successful authentication with valid credentials
- [ ] Test authentication failure with invalid credentials
- [ ] Test tenant-specific login flows
- [ ] Test owner login without tenant
- [ ] Test login tracking updates
- [ ] Test JWT token generation
- [ ] Test user creation with validation
- [ ] Test email uniqueness enforcement
- [ ] Test error handling scenarios

## 📊 Validation Checklist
- [ ] Authentication response time <200ms
- [ ] Password verification is secure
- [ ] JWT tokens contain all required claims
- [ ] Login tracking works correctly
- [ ] User creation validates properly
- [ ] Error handling is comprehensive
- [ ] Tenant isolation is maintained

## 🚨 Security Considerations
- [ ] Protect against timing attacks in authentication
- [ ] Prevent user enumeration through error messages
- [ ] Log authentication failures for monitoring
- [ ] Ensure tenant isolation in user lookup
- [ ] Validate all input parameters
- [ ] Use secure password comparison

## 📈 Performance Considerations
- [ ] Optimize database queries for authentication
- [ ] Minimize database roundtrips
- [ ] Cache frequently accessed data appropriately
- [ ] Ensure scalable authentication flow
- [ ] Monitor authentication performance

## 🔄 Implementation Steps
1. **Create AuthService class structure**
2. **Implement authenticate_user method**
3. **Add tenant-specific login logic**
4. **Implement token generation method**
5. **Add user creation method**
6. **Implement login tracking**
7. **Add comprehensive error handling**
8. **Write unit tests for all methods**

## 📝 Code Example
```python
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from datetime import datetime, timedelta
from typing import Optional

from ..models.user import User
from ..models.tenant import Tenant
from ..schemas.auth import UserCreate, LoginRequest
from ..core.security import verify_password, get_password_hash, create_access_token
from ..core.tenant_context import tenant_context
from ..core.config import settings

class AuthService:
    def __init__(self, db: Session):
        self.db = db

    def authenticate_user(self, login_data: LoginRequest) -> Optional[User]:
        """Authenticate user with email and password"""
        # First, find the tenant if provided
        tenant = None
        if login_data.tenant_slug:
            tenant = self.db.query(Tenant).filter(
                Tenant.slug == login_data.tenant_slug,
                Tenant.is_active == True
            ).first()
            if not tenant:
                return None

        # Find user by email
        if tenant:
            # Tenant-specific user lookup
            with tenant_context(self.db, tenant.id):
                user = self.db.query(User).filter(
                    User.email == login_data.email,
                    User.is_active == True
                ).first()
        else:
            # Owner login without tenant slug
            user = self.db.query(User).filter(
                User.email == login_data.email,
                User.role == "owner",
                User.is_active == True
            ).first()

        # Verify password
        if not user or not verify_password(login_data.password, user.hashed_password):
            return None

        # Update login tracking
        user.last_login = datetime.utcnow()
        user.login_count += 1
        self.db.commit()

        return user

    def create_access_token_for_user(self, user: User) -> dict:
        """Create access token for authenticated user"""
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

        token_data = {
            "sub": user.id,
            "tenant_id": user.tenant_id,
            "role": user.role,
            "email": user.email
        }

        access_token = create_access_token(
            data=token_data,
            expires_delta=access_token_expires
        )

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": user
        }

    def create_user(self, user_data: UserCreate) -> User:
        """Create a new user"""
        # Check if user already exists
        with tenant_context(self.db, user_data.tenant_id):
            existing_user = self.db.query(User).filter(
                User.email == user_data.email
            ).first()

            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )

            # Create new user
            hashed_password = get_password_hash(user_data.password)
            db_user = User(
                email=user_data.email,
                username=user_data.username,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                hashed_password=hashed_password,
                role=user_data.role,
                tenant_id=user_data.tenant_id
            )

            self.db.add(db_user)
            self.db.commit()
            self.db.refresh(db_user)

            return db_user

    def verify_user_email(self, user_id: str, verification_token: str) -> bool:
        """Verify user email with token"""
        # Implementation for email verification
        pass

    def request_password_reset(self, email: str, tenant_slug: str) -> bool:
        """Request password reset for user"""
        # Implementation for password reset request
        pass

    def reset_password(self, reset_token: str, new_password: str) -> bool:
        """Reset user password with token"""
        # Implementation for password reset
        pass
```

## 🔍 Test Examples
```python
def test_authenticate_user_success(test_db, sample_tenant):
    """Test successful user authentication"""
    auth_service = AuthService(test_db)
    
    # Create test user
    user_data = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        tenant_id=sample_tenant.id
    )
    user = auth_service.create_user(user_data)
    
    # Test authentication
    login_data = LoginRequest(
        email="<EMAIL>",
        password="testpassword123",
        tenant_slug=sample_tenant.slug
    )
    
    authenticated_user = auth_service.authenticate_user(login_data)
    
    assert authenticated_user is not None
    assert authenticated_user.id == user.id
    assert authenticated_user.login_count == 1

def test_authenticate_user_invalid_password(test_db, sample_tenant):
    """Test authentication with invalid password"""
    auth_service = AuthService(test_db)
    
    login_data = LoginRequest(
        email="<EMAIL>",
        password="wrongpassword",
        tenant_slug=sample_tenant.slug
    )
    
    authenticated_user = auth_service.authenticate_user(login_data)
    assert authenticated_user is None

def test_create_access_token(test_db, sample_user):
    """Test JWT token creation"""
    auth_service = AuthService(test_db)
    
    token_response = auth_service.create_access_token_for_user(sample_user)
    
    assert "access_token" in token_response
    assert token_response["token_type"] == "bearer"
    assert "expires_in" in token_response
    assert token_response["user"].id == sample_user.id
```

## 🎯 Definition of Done
- [ ] AuthService class is fully implemented
- [ ] User authentication works correctly
- [ ] JWT token generation includes all required claims
- [ ] User creation validates email uniqueness
- [ ] Login tracking updates properly
- [ ] Error handling is comprehensive
- [ ] All tests pass with >95% coverage
- [ ] Performance requirements are met
- [ ] Security review is completed
- [ ] Code review is approved
