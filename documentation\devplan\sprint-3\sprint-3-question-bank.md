# Sprint 3: Question Bank Implementation Guide

## Overview
This sprint implements the question bank system with data models, manual CRUD operations, OpenAI integration, question generation, tagging system, and quality validation.

## Timeline: 2 weeks

## Prerequisites
- Sprint 1 and 2 completed successfully
- OpenAI API key configured
- Database with RLS and user management working

## Step 1: Question Bank Data Models

### 1.1 Create Question Models
Create `backend/src/models/question.py`:
```python
from sqlalchemy import Column, String, Text, Integer, Boolean, JSON, Enum, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import ARRAY
import enum

from .base import Base, TimestampMixin, UUIDMixin, SoftDeleteMixin, TenantMixin

class QuestionType(enum.Enum):
    MULTIPLE_CHOICE = "multiple_choice"
    TRUE_FALSE = "true_false"
    SHORT_ANSWER = "short_answer"
    ESSAY = "essay"
    FILL_BLANK = "fill_blank"
    MATCHING = "matching"
    ORDERING = "ordering"
    LISTENING = "listening"
    SPEAKING = "speaking"
    READING = "reading"
    WRITING = "writing"

class DifficultyLevel(enum.Enum):
    BEGINNER = "beginner"
    ELEMENTARY = "elementary"
    INTERMEDIATE = "intermediate"
    UPPER_INTERMEDIATE = "upper_intermediate"
    ADVANCED = "advanced"
    PROFICIENT = "proficient"

class Question(Base, UUIDMixin, TimestampMixin, SoftDeleteMixin, TenantMixin):
    __tablename__ = "questions"
    
    # Basic information
    title = Column(String(500), nullable=False)
    content = Column(Text, nullable=False)
    question_type = Column(Enum(QuestionType), nullable=False)
    
    # Difficulty and categorization
    difficulty_level = Column(Enum(DifficultyLevel), nullable=False)
    subject = Column(String(100), nullable=True)
    topic = Column(String(200), nullable=True)
    
    # Question configuration
    options = Column(JSON, nullable=True)  # For MCQ, matching, etc.
    correct_answer = Column(JSON, nullable=True)  # Flexible answer storage
    explanation = Column(Text, nullable=True)
    
    # Scoring
    points = Column(Integer, default=1, nullable=False)
    time_limit = Column(Integer, nullable=True)  # in seconds
    
    # Media attachments
    audio_url = Column(String(500), nullable=True)
    image_url = Column(String(500), nullable=True)
    video_url = Column(String(500), nullable=True)
    
    # Metadata
    language = Column(String(10), default="en", nullable=False)
    version = Column(Integer, default=1, nullable=False)
    is_published = Column(Boolean, default=False, nullable=False)
    
    # AI generation metadata
    is_ai_generated = Column(Boolean, default=False, nullable=False)
    ai_prompt = Column(Text, nullable=True)
    ai_model = Column(String(100), nullable=True)
    
    # Quality metrics
    quality_score = Column(Integer, nullable=True)  # 1-100
    review_status = Column(String(50), default="pending", nullable=False)  # pending, approved, rejected
    
    # Usage statistics
    usage_count = Column(Integer, default=0, nullable=False)
    success_rate = Column(Integer, nullable=True)  # percentage
    
    # Author information
    created_by = Column(String, nullable=False)  # User ID
    reviewed_by = Column(String, nullable=True)  # User ID
    
    # Relationships
    tags = relationship("QuestionTag", back_populates="question", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Question(id={self.id}, title={self.title[:50]}, type={self.question_type})>"

class Tag(Base, UUIDMixin, TimestampMixin, TenantMixin):
    __tablename__ = "tags"
    
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    color = Column(String(7), default="#007bff", nullable=False)  # Hex color
    
    # Hierarchical structure
    parent_id = Column(String, ForeignKey("tags.id"), nullable=True)
    
    # Category information
    category = Column(String(100), nullable=True)  # grammar, vocabulary, etc.
    
    # Relationships
    parent = relationship("Tag", remote_side="Tag.id", backref="children")
    questions = relationship("QuestionTag", back_populates="tag")
    
    def __repr__(self):
        return f"<Tag(id={self.id}, name={self.name})>"

class QuestionTag(Base, UUIDMixin, TimestampMixin, TenantMixin):
    __tablename__ = "question_tags"
    
    question_id = Column(String, ForeignKey("questions.id"), nullable=False)
    tag_id = Column(String, ForeignKey("tags.id"), nullable=False)
    
    # Relationships
    question = relationship("Question", back_populates="tags")
    tag = relationship("Tag", back_populates="questions")
    
    def __repr__(self):
        return f"<QuestionTag(question_id={self.question_id}, tag_id={self.tag_id})>"
```

### 1.2 Create Question Schemas
Create `backend/src/schemas/question.py`:
```python
from pydantic import BaseModel, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

class QuestionTypeEnum(str, Enum):
    MULTIPLE_CHOICE = "multiple_choice"
    TRUE_FALSE = "true_false"
    SHORT_ANSWER = "short_answer"
    ESSAY = "essay"
    FILL_BLANK = "fill_blank"
    MATCHING = "matching"
    ORDERING = "ordering"
    LISTENING = "listening"
    SPEAKING = "speaking"
    READING = "reading"
    WRITING = "writing"

class DifficultyLevelEnum(str, Enum):
    BEGINNER = "beginner"
    ELEMENTARY = "elementary"
    INTERMEDIATE = "intermediate"
    UPPER_INTERMEDIATE = "upper_intermediate"
    ADVANCED = "advanced"
    PROFICIENT = "proficient"

class QuestionBase(BaseModel):
    title: str
    content: str
    question_type: QuestionTypeEnum
    difficulty_level: DifficultyLevelEnum
    subject: Optional[str] = None
    topic: Optional[str] = None
    options: Optional[Dict[str, Any]] = None
    correct_answer: Optional[Dict[str, Any]] = None
    explanation: Optional[str] = None
    points: int = 1
    time_limit: Optional[int] = None
    language: str = "en"

class QuestionCreate(QuestionBase):
    tag_ids: Optional[List[str]] = []
    
    @validator('title')
    def validate_title(cls, v):
        if len(v.strip()) < 5:
            raise ValueError('Title must be at least 5 characters long')
        return v.strip()
    
    @validator('content')
    def validate_content(cls, v):
        if len(v.strip()) < 10:
            raise ValueError('Content must be at least 10 characters long')
        return v.strip()

class QuestionUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    difficulty_level: Optional[DifficultyLevelEnum] = None
    subject: Optional[str] = None
    topic: Optional[str] = None
    options: Optional[Dict[str, Any]] = None
    correct_answer: Optional[Dict[str, Any]] = None
    explanation: Optional[str] = None
    points: Optional[int] = None
    time_limit: Optional[int] = None
    is_published: Optional[bool] = None
    tag_ids: Optional[List[str]] = None

class TagBase(BaseModel):
    name: str
    description: Optional[str] = None
    color: str = "#007bff"
    category: Optional[str] = None
    parent_id: Optional[str] = None

class TagCreate(TagBase):
    pass

class TagInDB(TagBase):
    id: str
    tenant_id: str
    created_at: datetime
    
    class Config:
        from_attributes = True

class QuestionInDB(QuestionBase):
    id: str
    tenant_id: str
    version: int
    is_published: bool
    is_ai_generated: bool
    quality_score: Optional[int]
    review_status: str
    usage_count: int
    success_rate: Optional[int]
    created_by: str
    created_at: datetime
    updated_at: datetime
    tags: List[TagInDB] = []
    
    class Config:
        from_attributes = True

class QuestionFilter(BaseModel):
    question_type: Optional[QuestionTypeEnum] = None
    difficulty_level: Optional[DifficultyLevelEnum] = None
    subject: Optional[str] = None
    topic: Optional[str] = None
    tag_ids: Optional[List[str]] = None
    is_published: Optional[bool] = None
    is_ai_generated: Optional[bool] = None
    language: Optional[str] = None
    search: Optional[str] = None

class QuestionGenerate(BaseModel):
    question_type: QuestionTypeEnum
    difficulty_level: DifficultyLevelEnum
    subject: str
    topic: str
    count: int = 1
    language: str = "en"
    additional_instructions: Optional[str] = None
    
    @validator('count')
    def validate_count(cls, v):
        if v < 1 or v > 10:
            raise ValueError('Count must be between 1 and 10')
        return v
```

## Step 2: OpenAI Integration Service

### 2.1 Create OpenAI Service
Create `backend/src/services/openai_service.py`:
```python
import openai
from typing import List, Dict, Any, Optional
import json
import logging
from datetime import datetime

from ..core.config import settings
from ..schemas.question import QuestionTypeEnum, DifficultyLevelEnum

logger = logging.getLogger(__name__)

class OpenAIService:
    def __init__(self):
        if not settings.OPENAI_API_KEY:
            raise ValueError("OpenAI API key not configured")

        openai.api_key = settings.OPENAI_API_KEY
        self.model = "gpt-4"
        self.max_tokens = 2000
        self.temperature = 0.7

    def generate_questions(
        self,
        question_type: QuestionTypeEnum,
        difficulty_level: DifficultyLevelEnum,
        subject: str,
        topic: str,
        count: int = 1,
        language: str = "en",
        additional_instructions: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Generate questions using OpenAI"""

        prompt = self._build_prompt(
            question_type, difficulty_level, subject, topic,
            count, language, additional_instructions
        )

        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self._get_system_prompt()},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                n=1
            )

            content = response.choices[0].message.content
            questions = self._parse_response(content, question_type)

            logger.info(f"Generated {len(questions)} questions for {subject}/{topic}")
            return questions

        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise Exception(f"Failed to generate questions: {str(e)}")

    def _get_system_prompt(self) -> str:
        """Get the system prompt for question generation"""
        return """You are an expert educational content creator specializing in generating high-quality assessment questions.

Your task is to create educational questions that are:
- Pedagogically sound and appropriate for the specified difficulty level
- Clear, unambiguous, and well-structured
- Culturally neutral and inclusive
- Properly formatted according to the question type requirements

Always respond with valid JSON format containing an array of question objects."""

    def _build_prompt(
        self,
        question_type: QuestionTypeEnum,
        difficulty_level: DifficultyLevelEnum,
        subject: str,
        topic: str,
        count: int,
        language: str,
        additional_instructions: Optional[str]
    ) -> str:
        """Build the user prompt for question generation"""

        type_instructions = self._get_type_instructions(question_type)
        difficulty_instructions = self._get_difficulty_instructions(difficulty_level)

        prompt = f"""Generate {count} {question_type.value} question(s) about {topic} in the subject of {subject}.

Difficulty Level: {difficulty_level.value}
Language: {language}

{type_instructions}

{difficulty_instructions}

Requirements:
- Each question must be unique and educational
- Include clear, correct answers
- Provide brief explanations for the correct answers
- Ensure questions are appropriate for the difficulty level
- Use proper grammar and spelling
- Make questions engaging and relevant

{additional_instructions or ""}

Return the response as a JSON array with this exact structure:
[
  {{
    "title": "Brief question title",
    "content": "The main question text",
    "options": {{}}, // For MCQ, matching, etc. - format according to question type
    "correct_answer": {{}}, // The correct answer in appropriate format
    "explanation": "Brief explanation of the correct answer",
    "points": 1, // Suggested point value
    "time_limit": null // Suggested time limit in seconds, or null
  }}
]"""

        return prompt

    def _get_type_instructions(self, question_type: QuestionTypeEnum) -> str:
        """Get specific instructions for each question type"""
        instructions = {
            QuestionTypeEnum.MULTIPLE_CHOICE: """
For multiple choice questions:
- Provide 4 options (A, B, C, D)
- Only one correct answer
- Make distractors plausible but clearly incorrect
- Format options as: {"A": "option1", "B": "option2", "C": "option3", "D": "option4"}
- Format correct_answer as: {"correct": "A"}
            """,
            QuestionTypeEnum.TRUE_FALSE: """
For true/false questions:
- Create clear statements that are definitively true or false
- Avoid ambiguous or opinion-based statements
- Format options as: {"true": "True", "false": "False"}
- Format correct_answer as: {"correct": "true"} or {"correct": "false"}
            """,
            QuestionTypeEnum.SHORT_ANSWER: """
For short answer questions:
- Expect 1-3 word answers
- Questions should have clear, factual answers
- Format correct_answer as: {"answer": "expected answer", "alternatives": ["alt1", "alt2"]}
            """,
            QuestionTypeEnum.ESSAY: """
For essay questions:
- Create open-ended questions requiring detailed responses
- Provide key points that should be covered
- Format correct_answer as: {"key_points": ["point1", "point2"], "sample_answer": "example response"}
            """,
            QuestionTypeEnum.WRITING: """
For writing questions:
- Create prompts that encourage creative or analytical writing
- Specify the expected length and format
- Format correct_answer as: {"criteria": ["criterion1", "criterion2"], "sample_response": "example"}
            """
        }

        return instructions.get(question_type, "Create appropriate questions for this type.")

    def _get_difficulty_instructions(self, difficulty_level: DifficultyLevelEnum) -> str:
        """Get instructions based on difficulty level"""
        instructions = {
            DifficultyLevelEnum.BEGINNER: "Use simple vocabulary and basic concepts. Focus on fundamental knowledge.",
            DifficultyLevelEnum.ELEMENTARY: "Use clear language with some complexity. Test basic understanding and application.",
            DifficultyLevelEnum.INTERMEDIATE: "Use moderate complexity. Test comprehension and analysis skills.",
            DifficultyLevelEnum.UPPER_INTERMEDIATE: "Use advanced vocabulary. Test synthesis and evaluation skills.",
            DifficultyLevelEnum.ADVANCED: "Use sophisticated language. Test critical thinking and complex analysis.",
            DifficultyLevelEnum.PROFICIENT: "Use expert-level complexity. Test mastery and creative application."
        }

        return instructions.get(difficulty_level, "Adjust difficulty appropriately.")

    def _parse_response(self, content: str, question_type: QuestionTypeEnum) -> List[Dict[str, Any]]:
        """Parse OpenAI response into structured question data"""
        try:
            # Try to extract JSON from the response
            start_idx = content.find('[')
            end_idx = content.rfind(']') + 1

            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON array found in response")

            json_str = content[start_idx:end_idx]
            questions = json.loads(json_str)

            # Validate and clean the questions
            validated_questions = []
            for q in questions:
                if self._validate_question(q, question_type):
                    validated_questions.append(q)

            return validated_questions

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            raise ValueError("Invalid JSON response from OpenAI")
        except Exception as e:
            logger.error(f"Error parsing response: {e}")
            raise ValueError(f"Failed to parse response: {str(e)}")

    def _validate_question(self, question: Dict[str, Any], question_type: QuestionTypeEnum) -> bool:
        """Validate a generated question"""
        required_fields = ['title', 'content', 'correct_answer', 'explanation']

        for field in required_fields:
            if field not in question or not question[field]:
                logger.warning(f"Question missing required field: {field}")
                return False

        # Type-specific validation
        if question_type == QuestionTypeEnum.MULTIPLE_CHOICE:
            if 'options' not in question or len(question['options']) != 4:
                logger.warning("MCQ question missing proper options")
                return False

        return True

    def validate_content_quality(self, content: str) -> Dict[str, Any]:
        """Use AI to validate content quality"""
        prompt = f"""Analyze the following educational content for quality and appropriateness:

Content: {content}

Evaluate on these criteria:
1. Clarity and readability
2. Educational value
3. Accuracy
4. Bias or inappropriate content
5. Grammar and spelling

Return a JSON response with:
{{
  "quality_score": 85, // 1-100 score
  "issues": ["list of any issues found"],
  "suggestions": ["list of improvement suggestions"],
  "bias_detected": false,
  "overall_assessment": "brief summary"
}}"""

        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an educational content quality analyst."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )

            content = response.choices[0].message.content
            return json.loads(content)

        except Exception as e:
            logger.error(f"Quality validation error: {e}")
            return {
                "quality_score": 50,
                "issues": ["Could not validate content"],
                "suggestions": [],
                "bias_detected": False,
                "overall_assessment": "Validation failed"
            }
```

## Step 3: Question Service Implementation

### 3.1 Create Question Service
Create `backend/src/services/question_service.py`:
```python
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from fastapi import HTTPException, status
from typing import List, Optional, Dict, Any
import logging

from ..models.question import Question, Tag, QuestionTag
from ..models.user import User
from ..schemas.question import (
    QuestionCreate, QuestionUpdate, QuestionFilter,
    QuestionGenerate, TagCreate
)
from ..core.tenant_context import tenant_context
from .openai_service import OpenAIService

logger = logging.getLogger(__name__)

class QuestionService:
    def __init__(self, db: Session):
        self.db = db
        self.openai_service = OpenAIService()

    def create_question(
        self,
        question_data: QuestionCreate,
        tenant_id: str,
        user_id: str
    ) -> Question:
        """Create a new question"""
        with tenant_context(self.db, tenant_id, user_id):
            # Create question
            question = Question(
                title=question_data.title,
                content=question_data.content,
                question_type=question_data.question_type,
                difficulty_level=question_data.difficulty_level,
                subject=question_data.subject,
                topic=question_data.topic,
                options=question_data.options,
                correct_answer=question_data.correct_answer,
                explanation=question_data.explanation,
                points=question_data.points,
                time_limit=question_data.time_limit,
                language=question_data.language,
                tenant_id=tenant_id,
                created_by=user_id
            )

            self.db.add(question)
            self.db.flush()  # Get the ID

            # Add tags
            if question_data.tag_ids:
                self._add_tags_to_question(question.id, question_data.tag_ids, tenant_id)

            self.db.commit()
            self.db.refresh(question)

            logger.info(f"Created question {question.id} by user {user_id}")
            return question

    def generate_questions(
        self,
        generation_data: QuestionGenerate,
        tenant_id: str,
        user_id: str
    ) -> List[Question]:
        """Generate questions using AI"""
        try:
            # Generate questions using OpenAI
            generated_data = self.openai_service.generate_questions(
                question_type=generation_data.question_type,
                difficulty_level=generation_data.difficulty_level,
                subject=generation_data.subject,
                topic=generation_data.topic,
                count=generation_data.count,
                language=generation_data.language,
                additional_instructions=generation_data.additional_instructions
            )

            questions = []
            with tenant_context(self.db, tenant_id, user_id):
                for q_data in generated_data:
                    question = Question(
                        title=q_data['title'],
                        content=q_data['content'],
                        question_type=generation_data.question_type,
                        difficulty_level=generation_data.difficulty_level,
                        subject=generation_data.subject,
                        topic=generation_data.topic,
                        options=q_data.get('options'),
                        correct_answer=q_data.get('correct_answer'),
                        explanation=q_data.get('explanation'),
                        points=q_data.get('points', 1),
                        time_limit=q_data.get('time_limit'),
                        language=generation_data.language,
                        tenant_id=tenant_id,
                        created_by=user_id,
                        is_ai_generated=True,
                        ai_model="gpt-4",
                        ai_prompt=str(generation_data.dict())
                    )

                    self.db.add(question)
                    questions.append(question)

                self.db.commit()

                # Refresh all questions to get IDs
                for question in questions:
                    self.db.refresh(question)

            logger.info(f"Generated {len(questions)} questions for {generation_data.subject}/{generation_data.topic}")
            return questions

        except Exception as e:
            logger.error(f"Question generation failed: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate questions: {str(e)}"
            )

    def get_questions(
        self,
        tenant_id: str,
        filters: Optional[QuestionFilter] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[Question]:
        """Get questions with optional filtering"""
        with tenant_context(self.db, tenant_id):
            query = self.db.query(Question).options(
                joinedload(Question.tags).joinedload(QuestionTag.tag)
            ).filter(Question.is_deleted == False)

            if filters:
                query = self._apply_filters(query, filters)

            return query.offset(skip).limit(limit).all()

    def get_question_by_id(self, question_id: str, tenant_id: str) -> Optional[Question]:
        """Get a specific question by ID"""
        with tenant_context(self.db, tenant_id):
            return self.db.query(Question).options(
                joinedload(Question.tags).joinedload(QuestionTag.tag)
            ).filter(
                Question.id == question_id,
                Question.is_deleted == False
            ).first()

    def update_question(
        self,
        question_id: str,
        question_data: QuestionUpdate,
        tenant_id: str,
        user_id: str
    ) -> Question:
        """Update a question"""
        with tenant_context(self.db, tenant_id, user_id):
            question = self.get_question_by_id(question_id, tenant_id)

            if not question:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Question not found"
                )

            # Update fields
            update_data = question_data.dict(exclude_unset=True, exclude={'tag_ids'})
            for field, value in update_data.items():
                setattr(question, field, value)

            # Update version
            question.version += 1

            # Update tags if provided
            if question_data.tag_ids is not None:
                self._update_question_tags(question_id, question_data.tag_ids, tenant_id)

            self.db.commit()
            self.db.refresh(question)

            return question

    def delete_question(self, question_id: str, tenant_id: str) -> bool:
        """Soft delete a question"""
        with tenant_context(self.db, tenant_id):
            question = self.get_question_by_id(question_id, tenant_id)

            if not question:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Question not found"
                )

            question.soft_delete()
            self.db.commit()

            return True

    def _apply_filters(self, query, filters: QuestionFilter):
        """Apply filters to question query"""
        if filters.question_type:
            query = query.filter(Question.question_type == filters.question_type)

        if filters.difficulty_level:
            query = query.filter(Question.difficulty_level == filters.difficulty_level)

        if filters.subject:
            query = query.filter(Question.subject.ilike(f"%{filters.subject}%"))

        if filters.topic:
            query = query.filter(Question.topic.ilike(f"%{filters.topic}%"))

        if filters.is_published is not None:
            query = query.filter(Question.is_published == filters.is_published)

        if filters.is_ai_generated is not None:
            query = query.filter(Question.is_ai_generated == filters.is_ai_generated)

        if filters.language:
            query = query.filter(Question.language == filters.language)

        if filters.search:
            search_term = f"%{filters.search}%"
            query = query.filter(
                or_(
                    Question.title.ilike(search_term),
                    Question.content.ilike(search_term),
                    Question.subject.ilike(search_term),
                    Question.topic.ilike(search_term)
                )
            )

        if filters.tag_ids:
            query = query.join(QuestionTag).filter(
                QuestionTag.tag_id.in_(filters.tag_ids)
            )

        return query

    def _add_tags_to_question(self, question_id: str, tag_ids: List[str], tenant_id: str):
        """Add tags to a question"""
        for tag_id in tag_ids:
            question_tag = QuestionTag(
                question_id=question_id,
                tag_id=tag_id,
                tenant_id=tenant_id
            )
            self.db.add(question_tag)

    def _update_question_tags(self, question_id: str, tag_ids: List[str], tenant_id: str):
        """Update question tags"""
        # Remove existing tags
        self.db.query(QuestionTag).filter(
            QuestionTag.question_id == question_id
        ).delete()

        # Add new tags
        self._add_tags_to_question(question_id, tag_ids, tenant_id)
```

## Step 4: Tag Management Service

### 4.1 Create Tag Service
Create `backend/src/services/tag_service.py`:
```python
from sqlalchemy.orm import Session
from sqlalchemy import and_
from fastapi import HTTPException, status
from typing import List, Optional

from ..models.question import Tag
from ..schemas.question import TagCreate
from ..core.tenant_context import tenant_context

class TagService:
    def __init__(self, db: Session):
        self.db = db

    def create_tag(self, tag_data: TagCreate, tenant_id: str) -> Tag:
        """Create a new tag"""
        with tenant_context(self.db, tenant_id):
            # Check if tag already exists
            existing_tag = self.db.query(Tag).filter(
                and_(
                    Tag.name.ilike(tag_data.name),
                    Tag.tenant_id == tenant_id
                )
            ).first()

            if existing_tag:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Tag with this name already exists"
                )

            # Validate parent tag if provided
            if tag_data.parent_id:
                parent_tag = self.db.query(Tag).filter(
                    and_(
                        Tag.id == tag_data.parent_id,
                        Tag.tenant_id == tenant_id
                    )
                ).first()

                if not parent_tag:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Parent tag not found"
                    )

            tag = Tag(
                name=tag_data.name,
                description=tag_data.description,
                color=tag_data.color,
                category=tag_data.category,
                parent_id=tag_data.parent_id,
                tenant_id=tenant_id
            )

            self.db.add(tag)
            self.db.commit()
            self.db.refresh(tag)

            return tag

    def get_tags(self, tenant_id: str, category: Optional[str] = None) -> List[Tag]:
        """Get all tags for a tenant"""
        with tenant_context(self.db, tenant_id):
            query = self.db.query(Tag)

            if category:
                query = query.filter(Tag.category == category)

            return query.order_by(Tag.name).all()

    def get_tag_hierarchy(self, tenant_id: str) -> List[Tag]:
        """Get tags organized in hierarchical structure"""
        with tenant_context(self.db, tenant_id):
            # Get root tags (no parent)
            root_tags = self.db.query(Tag).filter(
                Tag.parent_id.is_(None)
            ).order_by(Tag.name).all()

            # For each root tag, load its children recursively
            for tag in root_tags:
                self._load_tag_children(tag, tenant_id)

            return root_tags

    def _load_tag_children(self, tag: Tag, tenant_id: str):
        """Recursively load tag children"""
        children = self.db.query(Tag).filter(
            Tag.parent_id == tag.id
        ).order_by(Tag.name).all()

        tag.children = children

        for child in children:
            self._load_tag_children(child, tenant_id)

    def update_tag(self, tag_id: str, tag_data: TagCreate, tenant_id: str) -> Tag:
        """Update a tag"""
        with tenant_context(self.db, tenant_id):
            tag = self.db.query(Tag).filter(
                and_(
                    Tag.id == tag_id,
                    Tag.tenant_id == tenant_id
                )
            ).first()

            if not tag:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Tag not found"
                )

            # Update fields
            update_data = tag_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(tag, field, value)

            self.db.commit()
            self.db.refresh(tag)

            return tag

    def delete_tag(self, tag_id: str, tenant_id: str) -> bool:
        """Delete a tag"""
        with tenant_context(self.db, tenant_id):
            tag = self.db.query(Tag).filter(
                and_(
                    Tag.id == tag_id,
                    Tag.tenant_id == tenant_id
                )
            ).first()

            if not tag:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Tag not found"
                )

            # Check if tag has children
            children = self.db.query(Tag).filter(Tag.parent_id == tag_id).first()
            if children:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot delete tag with children. Delete children first."
                )

            self.db.delete(tag)
            self.db.commit()

            return True
```

## Step 5: API Endpoints Implementation

### 5.1 Create Question Endpoints
Create `backend/src/api/v1/endpoints/questions.py`:
```python
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from ....core.database import get_db
from ....core.deps import get_current_active_user, get_current_tenant
from ....models.user import User
from ....models.tenant import Tenant
from ....schemas.question import (
    QuestionInDB, QuestionCreate, QuestionUpdate,
    QuestionFilter, QuestionGenerate
)
from ....services.question_service import QuestionService

router = APIRouter()

@router.post("/", response_model=QuestionInDB)
async def create_question(
    question_data: QuestionCreate,
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Create a new question"""
    question_service = QuestionService(db)
    return question_service.create_question(
        question_data, current_tenant.id, current_user.id
    )

@router.post("/generate", response_model=List[QuestionInDB])
async def generate_questions(
    generation_data: QuestionGenerate,
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Generate questions using AI"""
    question_service = QuestionService(db)
    return question_service.generate_questions(
        generation_data, current_tenant.id, current_user.id
    )

@router.get("/", response_model=List[QuestionInDB])
async def get_questions(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    question_type: Optional[str] = Query(None),
    difficulty_level: Optional[str] = Query(None),
    subject: Optional[str] = Query(None),
    topic: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    is_published: Optional[bool] = Query(None),
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get questions with optional filtering"""
    filters = QuestionFilter(
        question_type=question_type,
        difficulty_level=difficulty_level,
        subject=subject,
        topic=topic,
        search=search,
        is_published=is_published
    )

    question_service = QuestionService(db)
    return question_service.get_questions(
        current_tenant.id, filters, skip, limit
    )

@router.get("/{question_id}", response_model=QuestionInDB)
async def get_question(
    question_id: str,
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get a specific question"""
    question_service = QuestionService(db)
    question = question_service.get_question_by_id(question_id, current_tenant.id)

    if not question:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Question not found"
        )

    return question

@router.put("/{question_id}", response_model=QuestionInDB)
async def update_question(
    question_id: str,
    question_data: QuestionUpdate,
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Update a question"""
    question_service = QuestionService(db)
    return question_service.update_question(
        question_id, question_data, current_tenant.id, current_user.id
    )

@router.delete("/{question_id}")
async def delete_question(
    question_id: str,
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Delete a question"""
    question_service = QuestionService(db)
    question_service.delete_question(question_id, current_tenant.id)

    return {"message": "Question deleted successfully"}
```

### 5.2 Create Tag Endpoints
Create `backend/src/api/v1/endpoints/tags.py`:
```python
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional

from ....core.database import get_db
from ....core.deps import get_current_active_user, get_current_tenant
from ....models.user import User
from ....models.tenant import Tenant
from ....schemas.question import TagInDB, TagCreate
from ....services.tag_service import TagService

router = APIRouter()

@router.post("/", response_model=TagInDB)
async def create_tag(
    tag_data: TagCreate,
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Create a new tag"""
    tag_service = TagService(db)
    return tag_service.create_tag(tag_data, current_tenant.id)

@router.get("/", response_model=List[TagInDB])
async def get_tags(
    category: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get all tags"""
    tag_service = TagService(db)
    return tag_service.get_tags(current_tenant.id, category)

@router.get("/hierarchy", response_model=List[TagInDB])
async def get_tag_hierarchy(
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get tags in hierarchical structure"""
    tag_service = TagService(db)
    return tag_service.get_tag_hierarchy(current_tenant.id)

@router.put("/{tag_id}", response_model=TagInDB)
async def update_tag(
    tag_id: str,
    tag_data: TagCreate,
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Update a tag"""
    tag_service = TagService(db)
    return tag_service.update_tag(tag_id, tag_data, current_tenant.id)

@router.delete("/{tag_id}")
async def delete_tag(
    tag_id: str,
    current_user: User = Depends(get_current_active_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Delete a tag"""
    tag_service = TagService(db)
    tag_service.delete_tag(tag_id, current_tenant.id)

    return {"message": "Tag deleted successfully"}
```

## Step 6: Quality Validation Service

### 6.1 Create Quality Validation Service
Create `backend/src/services/quality_service.py`:
```python
from sqlalchemy.orm import Session
from typing import Dict, Any, List
import logging
import re
from textstat import flesch_reading_ease, flesch_kincaid_grade
import spacy

from ..models.question import Question
from ..services.openai_service import OpenAIService
from ..core.tenant_context import tenant_context

logger = logging.getLogger(__name__)

class QualityValidationService:
    def __init__(self, db: Session):
        self.db = db
        self.openai_service = OpenAIService()

        # Load spaCy model for text analysis
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            logger.warning("spaCy English model not found. Some quality checks will be limited.")
            self.nlp = None

    def validate_question_quality(self, question: Question) -> Dict[str, Any]:
        """Comprehensive quality validation for a question"""
        results = {
            "overall_score": 0,
            "checks": {},
            "issues": [],
            "suggestions": [],
            "bias_detected": False
        }

        # Basic text quality checks
        results["checks"]["grammar"] = self._check_grammar(question.content)
        results["checks"]["readability"] = self._check_readability(question.content, question.difficulty_level)
        results["checks"]["clarity"] = self._check_clarity(question.content)
        results["checks"]["completeness"] = self._check_completeness(question)

        # Content-specific checks
        results["checks"]["answer_validity"] = self._check_answer_validity(question)
        results["checks"]["difficulty_alignment"] = self._check_difficulty_alignment(question)

        # AI-powered checks
        if self.openai_service:
            ai_validation = self.openai_service.validate_content_quality(question.content)
            results["checks"]["ai_assessment"] = ai_validation
            results["bias_detected"] = ai_validation.get("bias_detected", False)

        # Calculate overall score
        results["overall_score"] = self._calculate_overall_score(results["checks"])

        # Generate issues and suggestions
        results["issues"], results["suggestions"] = self._generate_feedback(results["checks"])

        return results

    def _check_grammar(self, text: str) -> Dict[str, Any]:
        """Check grammar and spelling"""
        issues = []
        score = 100

        # Basic checks
        if not text.strip():
            issues.append("Text is empty")
            score = 0

        # Check for common grammar issues
        if re.search(r'\b(there|their|they\'re)\b', text, re.IGNORECASE):
            # More sophisticated grammar checking would go here
            pass

        # Check for proper capitalization
        if not text[0].isupper():
            issues.append("Text should start with a capital letter")
            score -= 10

        # Check for proper punctuation
        if not text.rstrip().endswith(('.', '?', '!')):
            issues.append("Text should end with proper punctuation")
            score -= 10

        return {
            "score": max(0, score),
            "issues": issues,
            "passed": len(issues) == 0
        }

    def _check_readability(self, text: str, difficulty_level) -> Dict[str, Any]:
        """Check text readability against difficulty level"""
        try:
            flesch_score = flesch_reading_ease(text)
            grade_level = flesch_kincaid_grade(text)

            # Map difficulty levels to expected reading scores
            expected_ranges = {
                "beginner": (80, 100),      # Very easy
                "elementary": (70, 89),     # Easy
                "intermediate": (60, 79),   # Standard
                "upper_intermediate": (50, 69),  # Fairly difficult
                "advanced": (30, 59),       # Difficult
                "proficient": (0, 49)       # Very difficult
            }

            expected_min, expected_max = expected_ranges.get(difficulty_level.value, (0, 100))

            score = 100
            issues = []

            if flesch_score < expected_min:
                issues.append(f"Text may be too difficult for {difficulty_level.value} level")
                score -= 20
            elif flesch_score > expected_max:
                issues.append(f"Text may be too easy for {difficulty_level.value} level")
                score -= 20

            return {
                "score": score,
                "flesch_score": flesch_score,
                "grade_level": grade_level,
                "issues": issues,
                "passed": len(issues) == 0
            }

        except Exception as e:
            logger.error(f"Readability check failed: {e}")
            return {
                "score": 50,
                "issues": ["Could not analyze readability"],
                "passed": False
            }

    def _check_clarity(self, text: str) -> Dict[str, Any]:
        """Check text clarity and structure"""
        issues = []
        score = 100

        # Check sentence length
        sentences = re.split(r'[.!?]+', text)
        avg_sentence_length = sum(len(s.split()) for s in sentences if s.strip()) / max(len([s for s in sentences if s.strip()]), 1)

        if avg_sentence_length > 25:
            issues.append("Sentences may be too long for clarity")
            score -= 15

        # Check for ambiguous words
        ambiguous_words = ['this', 'that', 'it', 'they', 'them']
        word_count = len(text.split())
        ambiguous_count = sum(1 for word in ambiguous_words if word in text.lower())

        if ambiguous_count / max(word_count, 1) > 0.1:
            issues.append("Text contains many ambiguous pronouns")
            score -= 10

        # Check for question clarity (if it's a question)
        if '?' in text and not text.strip().endswith('?'):
            issues.append("Question format may be unclear")
            score -= 10

        return {
            "score": max(0, score),
            "avg_sentence_length": avg_sentence_length,
            "issues": issues,
            "passed": len(issues) == 0
        }

    def _check_completeness(self, question: Question) -> Dict[str, Any]:
        """Check if question has all required components"""
        issues = []
        score = 100

        # Check required fields
        if not question.title or len(question.title.strip()) < 5:
            issues.append("Question title is missing or too short")
            score -= 20

        if not question.content or len(question.content.strip()) < 10:
            issues.append("Question content is missing or too short")
            score -= 30

        if not question.correct_answer:
            issues.append("Correct answer is missing")
            score -= 30

        # Type-specific checks
        if question.question_type.value == "multiple_choice":
            if not question.options or len(question.options) < 2:
                issues.append("Multiple choice question needs at least 2 options")
                score -= 25

        if not question.explanation:
            issues.append("Explanation is missing (recommended)")
            score -= 10

        return {
            "score": max(0, score),
            "issues": issues,
            "passed": len(issues) == 0
        }

    def _check_answer_validity(self, question: Question) -> Dict[str, Any]:
        """Check if the answer is valid for the question type"""
        issues = []
        score = 100

        if not question.correct_answer:
            return {
                "score": 0,
                "issues": ["No correct answer provided"],
                "passed": False
            }

        # Type-specific validation
        if question.question_type.value == "multiple_choice":
            if question.options:
                correct_key = question.correct_answer.get("correct")
                if correct_key not in question.options:
                    issues.append("Correct answer key not found in options")
                    score -= 50

        elif question.question_type.value == "true_false":
            correct_value = question.correct_answer.get("correct", "").lower()
            if correct_value not in ["true", "false"]:
                issues.append("True/false answer must be 'true' or 'false'")
                score -= 50

        return {
            "score": max(0, score),
            "issues": issues,
            "passed": len(issues) == 0
        }

    def _check_difficulty_alignment(self, question: Question) -> Dict[str, Any]:
        """Check if question difficulty aligns with stated level"""
        # This would involve more sophisticated analysis
        # For now, basic heuristics

        score = 80  # Default assumption of reasonable alignment
        issues = []

        word_count = len(question.content.split())

        # Basic word count heuristics
        if question.difficulty_level.value == "beginner" and word_count > 50:
            issues.append("Question may be too long for beginner level")
            score -= 20
        elif question.difficulty_level.value == "advanced" and word_count < 20:
            issues.append("Question may be too simple for advanced level")
            score -= 20

        return {
            "score": score,
            "issues": issues,
            "passed": len(issues) == 0
        }

    def _calculate_overall_score(self, checks: Dict[str, Any]) -> int:
        """Calculate overall quality score from individual checks"""
        scores = []
        weights = {
            "grammar": 0.2,
            "readability": 0.15,
            "clarity": 0.15,
            "completeness": 0.25,
            "answer_validity": 0.2,
            "difficulty_alignment": 0.05
        }

        total_weight = 0
        weighted_sum = 0

        for check_name, weight in weights.items():
            if check_name in checks and "score" in checks[check_name]:
                weighted_sum += checks[check_name]["score"] * weight
                total_weight += weight

        # Include AI assessment if available
        if "ai_assessment" in checks and "quality_score" in checks["ai_assessment"]:
            ai_score = checks["ai_assessment"]["quality_score"]
            weighted_sum += ai_score * 0.3
            total_weight += 0.3

        return int(weighted_sum / max(total_weight, 1)) if total_weight > 0 else 50

    def _generate_feedback(self, checks: Dict[str, Any]) -> tuple:
        """Generate issues and suggestions from check results"""
        all_issues = []
        suggestions = []

        for check_name, check_result in checks.items():
            if isinstance(check_result, dict) and "issues" in check_result:
                all_issues.extend(check_result["issues"])

        # Generate suggestions based on issues
        if any("grammar" in issue.lower() for issue in all_issues):
            suggestions.append("Consider using a grammar checker or proofreading tool")

        if any("readability" in issue.lower() for issue in all_issues):
            suggestions.append("Adjust sentence complexity to match the difficulty level")

        if any("clarity" in issue.lower() for issue in all_issues):
            suggestions.append("Simplify sentence structure and use more specific language")

        return all_issues, suggestions

    def batch_validate_questions(self, tenant_id: str, question_ids: List[str]) -> Dict[str, Any]:
        """Validate multiple questions in batch"""
        results = {}

        with tenant_context(self.db, tenant_id):
            questions = self.db.query(Question).filter(
                Question.id.in_(question_ids)
            ).all()

            for question in questions:
                try:
                    validation_result = self.validate_question_quality(question)
                    results[question.id] = validation_result

                    # Update question quality score
                    question.quality_score = validation_result["overall_score"]

                except Exception as e:
                    logger.error(f"Validation failed for question {question.id}: {e}")
                    results[question.id] = {
                        "overall_score": 0,
                        "error": str(e)
                    }

            self.db.commit()

        return results
```

## Step 7: Testing Implementation

### 7.1 Create Sprint 3 Tests
Create `backend/tests/test_sprint3.py`:
```python
import pytest
from sqlalchemy.orm import Session
from unittest.mock import Mock, patch

from src.models.question import Question, Tag, QuestionTag
from src.models.tenant import Tenant
from src.models.user import User
from src.services.question_service import QuestionService
from src.services.tag_service import TagService
from src.services.quality_service import QualityValidationService
from src.schemas.question import (
    QuestionCreate, QuestionGenerate, TagCreate,
    QuestionTypeEnum, DifficultyLevelEnum
)

def test_question_creation(test_db: Session, sample_tenant: Tenant):
    """Test question creation"""
    # Create a user first
    from src.services.auth_service import AuthService
    from src.schemas.auth import UserCreate

    auth_service = AuthService(test_db)
    user_data = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        tenant_id=sample_tenant.id
    )
    user = auth_service.create_user(user_data)

    # Create question
    question_service = QuestionService(test_db)
    question_data = QuestionCreate(
        title="Test Question",
        content="What is 2 + 2?",
        question_type=QuestionTypeEnum.MULTIPLE_CHOICE,
        difficulty_level=DifficultyLevelEnum.BEGINNER,
        subject="Mathematics",
        topic="Basic Arithmetic",
        options={"A": "3", "B": "4", "C": "5", "D": "6"},
        correct_answer={"correct": "B"},
        explanation="2 + 2 equals 4"
    )

    question = question_service.create_question(
        question_data, sample_tenant.id, user.id
    )

    assert question.title == "Test Question"
    assert question.question_type == QuestionTypeEnum.MULTIPLE_CHOICE
    assert question.created_by == user.id

def test_tag_creation(test_db: Session, sample_tenant: Tenant):
    """Test tag creation and hierarchy"""
    tag_service = TagService(test_db)

    # Create parent tag
    parent_data = TagCreate(
        name="Mathematics",
        description="Math-related topics",
        category="subject"
    )
    parent_tag = tag_service.create_tag(parent_data, sample_tenant.id)

    # Create child tag
    child_data = TagCreate(
        name="Algebra",
        description="Algebraic concepts",
        category="topic",
        parent_id=parent_tag.id
    )
    child_tag = tag_service.create_tag(child_data, sample_tenant.id)

    assert parent_tag.name == "Mathematics"
    assert child_tag.parent_id == parent_tag.id

@patch('src.services.openai_service.openai.ChatCompletion.create')
def test_question_generation(mock_openai, test_db: Session, sample_tenant: Tenant):
    """Test AI question generation"""
    # Mock OpenAI response
    mock_response = Mock()
    mock_response.choices = [Mock()]
    mock_response.choices[0].message.content = '''[
        {
            "title": "Basic Addition",
            "content": "What is 3 + 5?",
            "options": {"A": "7", "B": "8", "C": "9", "D": "10"},
            "correct_answer": {"correct": "B"},
            "explanation": "3 + 5 = 8",
            "points": 1,
            "time_limit": null
        }
    ]'''
    mock_openai.return_value = mock_response

    # Create user
    from src.services.auth_service import AuthService
    from src.schemas.auth import UserCreate

    auth_service = AuthService(test_db)
    user_data = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        tenant_id=sample_tenant.id
    )
    user = auth_service.create_user(user_data)

    # Generate questions
    question_service = QuestionService(test_db)
    generation_data = QuestionGenerate(
        question_type=QuestionTypeEnum.MULTIPLE_CHOICE,
        difficulty_level=DifficultyLevelEnum.BEGINNER,
        subject="Mathematics",
        topic="Addition",
        count=1
    )

    questions = question_service.generate_questions(
        generation_data, sample_tenant.id, user.id
    )

    assert len(questions) == 1
    assert questions[0].is_ai_generated == True
    assert questions[0].title == "Basic Addition"

def test_quality_validation(test_db: Session, sample_tenant: Tenant):
    """Test question quality validation"""
    # Create a question with quality issues
    question = Question(
        title="bad",  # Too short
        content="what?",  # Too short and unclear
        question_type=QuestionTypeEnum.MULTIPLE_CHOICE,
        difficulty_level=DifficultyLevelEnum.BEGINNER,
        tenant_id=sample_tenant.id,
        created_by="test-user"
        # Missing correct_answer and options
    )

    test_db.add(question)
    test_db.commit()
    test_db.refresh(question)

    # Validate quality
    quality_service = QualityValidationService(test_db)
    result = quality_service.validate_question_quality(question)

    assert result["overall_score"] < 50  # Should be low quality
    assert len(result["issues"]) > 0
    assert not result["checks"]["completeness"]["passed"]

def test_question_filtering(test_db: Session, sample_tenant: Tenant):
    """Test question filtering functionality"""
    # Create user and questions
    from src.services.auth_service import AuthService
    from src.schemas.auth import UserCreate

    auth_service = AuthService(test_db)
    user_data = UserCreate(
        email="<EMAIL>",
        password="testpassword123",
        tenant_id=sample_tenant.id
    )
    user = auth_service.create_user(user_data)

    question_service = QuestionService(test_db)

    # Create questions with different properties
    questions_data = [
        {
            "title": "Math Question 1",
            "subject": "Mathematics",
            "difficulty_level": DifficultyLevelEnum.BEGINNER
        },
        {
            "title": "Science Question 1",
            "subject": "Science",
            "difficulty_level": DifficultyLevelEnum.INTERMEDIATE
        }
    ]

    for q_data in questions_data:
        question_data = QuestionCreate(
            title=q_data["title"],
            content="Test question content",
            question_type=QuestionTypeEnum.MULTIPLE_CHOICE,
            difficulty_level=q_data["difficulty_level"],
            subject=q_data["subject"],
            topic="Test Topic",
            options={"A": "1", "B": "2"},
            correct_answer={"correct": "A"}
        )

        question_service.create_question(
            question_data, sample_tenant.id, user.id
        )

    # Test filtering
    from src.schemas.question import QuestionFilter

    # Filter by subject
    math_filter = QuestionFilter(subject="Mathematics")
    math_questions = question_service.get_questions(
        sample_tenant.id, math_filter
    )

    assert len(math_questions) == 1
    assert math_questions[0].subject == "Mathematics"

    # Filter by difficulty
    beginner_filter = QuestionFilter(difficulty_level=DifficultyLevelEnum.BEGINNER)
    beginner_questions = question_service.get_questions(
        sample_tenant.id, beginner_filter
    )

    assert len(beginner_questions) == 1
    assert beginner_questions[0].difficulty_level == DifficultyLevelEnum.BEGINNER
```

## Step 8: Validation and Deployment

### 8.1 Create Validation Script
Create `scripts/validate-sprint3.sh`:
```bash
#!/bin/bash

echo "Validating Sprint 3 implementation..."

# Install required Python packages
echo "Installing additional dependencies..."
docker-compose exec backend pip install textstat spacy
docker-compose exec backend python -m spacy download en_core_web_sm

# Run tests
echo "Running Sprint 3 tests..."
docker-compose exec backend pytest tests/test_sprint3.py -v

# Test API endpoints
echo "Testing question API endpoints..."
curl -f http://localhost:8000/api/v1/questions/ || echo "Questions endpoint not accessible"
curl -f http://localhost:8000/api/v1/tags/ || echo "Tags endpoint not accessible"

# Test database tables
echo "Checking database tables..."
docker-compose exec postgres psql -U postgres -d arroyo_university -c "SELECT COUNT(*) FROM questions;"
docker-compose exec postgres psql -U postgres -d arroyo_university -c "SELECT COUNT(*) FROM tags;"
docker-compose exec postgres psql -U postgres -d arroyo_university -c "SELECT COUNT(*) FROM question_tags;"

echo "Sprint 3 validation complete!"
```

### 8.2 Update Requirements
Add to `backend/requirements.txt`:
```
openai==1.3.5
textstat==0.7.3
spacy==3.7.2
```

## Validation Checklist

### 8.3 Sprint 3 Validation
- [ ] Question models created successfully
- [ ] Tag system works with hierarchy
- [ ] OpenAI integration generates questions
- [ ] Manual question CRUD operations work
- [ ] Quality validation identifies issues
- [ ] API endpoints respond correctly
- [ ] Database RLS works with new tables
- [ ] All tests pass
- [ ] Question filtering works properly
- [ ] Tag management functions correctly

## Next Steps

After completing Sprint 3:
1. Test question generation with real OpenAI API
2. Verify quality validation accuracy
3. Test tag hierarchy functionality
4. Confirm all CRUD operations work
5. Validate tenant isolation for questions
6. Proceed to Sprint 4: AI Services Implementation

## Performance Considerations

- Database indexes on frequently queried fields
- Efficient tag hierarchy queries
- OpenAI API rate limiting and caching
- Quality validation caching for repeated checks
- Pagination for large question sets
```
```
```
