# Task 1.7: Add Authentication to Main App

## 📋 Task Overview
**User Story:** HU-2.1 - JWT Authentication System  
**Task ID:** 1.7  
**Estimated Time:** 1 hour  
**Priority:** Critical  
**Complexity:** Low  

## 🎯 Description
Integrate authentication endpoints into the main FastAPI application with proper configuration, CORS setup, security middleware, and OpenAPI documentation.

## 📦 Deliverables
- [ ] Update `backend/src/api/v1/api.py` to include auth router
- [ ] Update `backend/src/main.py` with security configuration
- [ ] Configure CORS for authentication endpoints
- [ ] Add security middleware if needed

## ✅ Acceptance Criteria
- [ ] Auth router is included in API v1 routes
- [ ] CORS is configured to allow authentication from frontend
- [ ] Security headers are properly set
- [ ] OpenAPI documentation includes authentication
- [ ] Application starts without errors

## 🔧 Technical Requirements

### Integration Points
1. **API Router Integration**
   - Include auth router in v1 API
   - Proper route prefixes and tags
   - Consistent error handling

2. **CORS Configuration**
   - Allow authentication headers
   - Support preflight requests
   - Secure origin configuration

3. **Security Middleware**
   - Security headers middleware
   - Request logging for auth endpoints
   - Rate limiting configuration

4. **OpenAPI Documentation**
   - Security schemes definition
   - Authentication flow documentation
   - Example requests and responses

## 🔗 Dependencies
- **Prerequisite:** Authentication endpoints (Task 1.6)
- **Configuration:** CORS and security settings
- **Documentation:** OpenAPI security schemes

## 📝 Code Example
```python
# backend/src/api/v1/api.py
from fastapi import APIRouter

from .endpoints import auth, users, tenants

api_router = APIRouter()

# Include authentication routes
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["authentication"]
)

# Include other routes
api_router.include_router(
    users.router,
    prefix="/users",
    tags=["users"]
)

api_router.include_router(
    tenants.router,
    prefix="/tenants",
    tags=["tenants"]
)

# backend/src/main.py
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import HTTPBearer
import uvicorn

from .api.v1.api import api_router
from .core.config import settings
from .core.database import engine
from .models import Base

# Create database tables
Base.metadata.create_all(bind=engine)

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="Multi-tenant platform API",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Security middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=[
        "Authorization",
        "Content-Type",
        "X-Requested-With",
        "Accept",
        "Origin",
        "Access-Control-Request-Method",
        "Access-Control-Request-Headers"
    ],
)

# Security headers middleware
@app.middleware("http")
async def add_security_headers(request, call_next):
    response = await call_next(request)
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    return response

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "version": settings.VERSION}

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "Welcome to the Multi-tenant Platform API",
        "version": settings.VERSION,
        "docs": "/docs",
        "redoc": "/redoc"
    }

# OpenAPI security configuration
from fastapi.openapi.utils import get_openapi

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=settings.PROJECT_NAME,
        version=settings.VERSION,
        description="Multi-tenant platform API with JWT authentication",
        routes=app.routes,
    )
    
    # Add security schemes
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "Enter JWT token"
        },
        "OAuth2": {
            "type": "oauth2",
            "flows": {
                "password": {
                    "tokenUrl": f"{settings.API_V1_STR}/auth/token",
                    "scopes": {}
                }
            }
        }
    }
    
    # Add global security requirement
    openapi_schema["security"] = [{"BearerAuth": []}]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )

# backend/src/core/config.py (additions)
from typing import List

class Settings(BaseSettings):
    # ... existing settings ...
    
    # CORS settings
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:3001"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]
    
    # Security settings
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 1440  # 24 hours
    
    # API settings
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Multi-tenant Platform"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    class Config:
        env_file = ".env"

settings = Settings()
```

## 🧪 Testing Requirements
- [ ] Test application startup without errors
- [ ] Test CORS configuration with preflight requests
- [ ] Test authentication endpoints accessibility
- [ ] Test OpenAPI documentation generation
- [ ] Test security headers are set
- [ ] Test health check endpoint
- [ ] Test API versioning works correctly

## 📊 Validation Checklist
- [ ] Application starts successfully
- [ ] All routes are accessible
- [ ] CORS allows frontend requests
- [ ] Security headers are present
- [ ] OpenAPI docs show authentication
- [ ] No configuration errors
- [ ] Performance is acceptable

## 🚨 Security Considerations
- [ ] CORS origins are properly restricted
- [ ] Security headers prevent common attacks
- [ ] Trusted host middleware prevents host header attacks
- [ ] Authentication is properly documented
- [ ] No sensitive information in error responses

## 📈 Performance Considerations
- [ ] Middleware overhead is minimal
- [ ] CORS preflight caching is enabled
- [ ] Static file serving is optimized
- [ ] Database connection pooling is configured

## 🎯 Definition of Done
- [ ] Authentication is integrated into main app
- [ ] CORS is properly configured
- [ ] Security middleware is active
- [ ] OpenAPI documentation includes auth
- [ ] Application starts without errors
- [ ] All endpoints are accessible
- [ ] Security headers are set
- [ ] Code review is completed
