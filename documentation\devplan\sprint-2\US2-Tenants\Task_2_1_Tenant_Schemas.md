# Task 2.1: Create Tenant Schemas

## 📋 Task Overview
**User Story:** HU-2.2 - Tenant CRUD Operations  
**Task ID:** 2.1  
**Estimated Time:** 1.5 hours  
**Priority:** Critical  
**Complexity:** Medium  

## 🎯 Description
Define Pydantic schemas for tenant operations and validation that support tenant creation, updates, and responses with proper validation rules for multi-tenant SaaS architecture.

## 📦 Deliverables
- [ ] Create `backend/src/schemas/tenant.py`
- [ ] Tenant creation schema with owner user information
- [ ] Tenant update schema for customization
- [ ] Tenant response schemas with statistics
- [ ] Proper validation rules for slug and domain

## ✅ Acceptance Criteria
- [ ] TenantCreate includes owner user fields (email, password, name)
- [ ] Slug validation ensures URL-safe characters only
- [ ] Domain validation for custom tenant domains
- [ ] TenantUpdate allows partial updates of settings
- [ ] TenantWithStats includes user count and activity metrics
- [ ] All schemas have proper validation and error messages
- [ ] Settings field supports flexible JSONB data

## 🔧 Technical Requirements

### Schema Categories
1. **Base Tenant Schemas**
   - TenantBase, TenantCreate, TenantUpdate, TenantInDB
2. **Extended Schemas**
   - TenantWithStats (includes user metrics)
   - TenantSettings (for customization)

### Validation Rules
- Slug: alphanumeric, hyphens, underscores only
- Domain: valid domain format (optional)
- Email: valid email format for owner
- Settings: flexible JSONB structure
- Colors: valid hex color codes

### Owner User Integration
- Owner email, password, and name fields
- Automatic owner user creation during tenant setup
- Proper validation for owner credentials

## 🔗 Dependencies
- **Prerequisite:** Pydantic library and validation utilities
- **Models:** Tenant model structure for reference
- **Standards:** Domain validation and slug formatting rules

## 🧪 Testing Requirements
- [ ] Test tenant creation schema validation
- [ ] Test slug format validation (URL-safe)
- [ ] Test domain format validation
- [ ] Test owner user field validation
- [ ] Test settings JSONB structure
- [ ] Test partial update schema
- [ ] Test statistics schema structure
- [ ] Test error message clarity

## 📊 Validation Checklist
- [ ] Slug validation prevents invalid characters
- [ ] Domain validation accepts valid domains
- [ ] Owner user validation is comprehensive
- [ ] Settings schema is flexible but validated
- [ ] Color validation accepts hex codes
- [ ] Error messages are user-friendly
- [ ] Type hints are complete

## 🚨 Security Considerations
- [ ] Slug validation prevents injection attacks
- [ ] Domain validation prevents malicious domains
- [ ] Owner password validation enforces security
- [ ] Settings validation prevents code injection
- [ ] Input sanitization for all text fields

## 📈 Performance Considerations
- [ ] Validation is fast and efficient
- [ ] Minimal memory usage for schemas
- [ ] Efficient serialization/deserialization
- [ ] No blocking operations in validation

## 🔄 Implementation Steps
1. **Create base tenant schemas**
2. **Add tenant creation schema with owner fields**
3. **Implement validation rules for slug and domain**
4. **Create update and response schemas**
5. **Add statistics and settings schemas**
6. **Implement comprehensive validation**
7. **Write validation tests**

## 📝 Code Example
```python
from pydantic import BaseModel, EmailStr, validator
from typing import Optional, Dict, Any
from datetime import datetime
import re

class TenantBase(BaseModel):
    name: str
    slug: str
    domain: Optional[str] = None
    is_active: bool = True
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = None
    address: Optional[str] = None

class TenantCreate(TenantBase):
    # Owner user information
    owner_email: EmailStr
    owner_password: str
    owner_first_name: Optional[str] = None
    owner_last_name: Optional[str] = None

    @validator('slug')
    def validate_slug(cls, v):
        if not v:
            raise ValueError('Slug is required')
        
        # Check for valid characters (alphanumeric, hyphens, underscores)
        if not re.match(r'^[a-zA-Z0-9_-]+$', v):
            raise ValueError('Slug must contain only alphanumeric characters, hyphens, and underscores')
        
        # Check length
        if len(v) < 3:
            raise ValueError('Slug must be at least 3 characters long')
        if len(v) > 50:
            raise ValueError('Slug must be no more than 50 characters long')
        
        return v.lower()

    @validator('domain')
    def validate_domain(cls, v):
        if v is not None:
            # Basic domain validation
            domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
            if not re.match(domain_pattern, v):
                raise ValueError('Invalid domain format')
        return v

    @validator('owner_password')
    def validate_owner_password(cls, v):
        if len(v) < 8:
            raise ValueError('Owner password must be at least 8 characters long')
        if not any(c.isdigit() for c in v):
            raise ValueError('Owner password must contain at least one digit')
        if not any(c.isalpha() for c in v):
            raise ValueError('Owner password must contain at least one letter')
        return v

    class Config:
        schema_extra = {
            "example": {
                "name": "Acme Corporation",
                "slug": "acme-corp",
                "domain": "acme.example.com",
                "contact_email": "<EMAIL>",
                "owner_email": "<EMAIL>",
                "owner_password": "securepassword123",
                "owner_first_name": "John",
                "owner_last_name": "Doe"
            }
        }

class TenantUpdate(BaseModel):
    name: Optional[str] = None
    domain: Optional[str] = None
    is_active: Optional[bool] = None
    settings: Optional[Dict[str, Any]] = None
    logo_url: Optional[str] = None
    primary_color: Optional[str] = None
    secondary_color: Optional[str] = None
    contact_email: Optional[EmailStr] = None
    contact_phone: Optional[str] = None
    address: Optional[str] = None

    @validator('domain')
    def validate_domain(cls, v):
        if v is not None:
            domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
            if not re.match(domain_pattern, v):
                raise ValueError('Invalid domain format')
        return v

    @validator('primary_color', 'secondary_color')
    def validate_color(cls, v):
        if v is not None:
            # Validate hex color format
            if not re.match(r'^#[0-9A-Fa-f]{6}$', v):
                raise ValueError('Color must be a valid hex color code (e.g., #FF0000)')
        return v

    @validator('logo_url')
    def validate_logo_url(cls, v):
        if v is not None:
            # Basic URL validation
            url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
            if not re.match(url_pattern, v):
                raise ValueError('Logo URL must be a valid HTTP/HTTPS URL')
        return v

class TenantInDB(TenantBase):
    id: str
    settings: Dict[str, Any] = {}
    logo_url: Optional[str] = None
    primary_color: str = "#007bff"
    secondary_color: str = "#6c757d"
    plan: str = "basic"
    max_users: int = 100
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class TenantWithStats(TenantInDB):
    user_count: int = 0
    active_user_count: int = 0
    last_activity: Optional[datetime] = None

class TenantSettings(BaseModel):
    """Schema for tenant-specific settings"""
    branding: Optional[Dict[str, Any]] = None
    features: Optional[Dict[str, bool]] = None
    notifications: Optional[Dict[str, Any]] = None
    integrations: Optional[Dict[str, Any]] = None
    
    class Config:
        schema_extra = {
            "example": {
                "branding": {
                    "logo_url": "https://example.com/logo.png",
                    "primary_color": "#007bff",
                    "secondary_color": "#6c757d"
                },
                "features": {
                    "advanced_analytics": True,
                    "custom_domains": False,
                    "api_access": True
                },
                "notifications": {
                    "email_enabled": True,
                    "slack_webhook": "https://hooks.slack.com/...",
                    "notification_frequency": "daily"
                }
            }
        }
```

## 🔍 Validation Examples
```python
def test_tenant_create_validation():
    """Test TenantCreate schema validation"""
    # Valid data
    valid_data = {
        "name": "Test Corp",
        "slug": "test-corp",
        "owner_email": "<EMAIL>",
        "owner_password": "securepass123"
    }
    tenant = TenantCreate(**valid_data)
    assert tenant.slug == "test-corp"
    
    # Invalid slug
    invalid_data = valid_data.copy()
    invalid_data["slug"] = "invalid slug!"
    
    with pytest.raises(ValueError, match="alphanumeric characters"):
        TenantCreate(**invalid_data)

def test_domain_validation():
    """Test domain validation"""
    valid_data = {
        "name": "Test Corp",
        "slug": "test-corp",
        "domain": "test.example.com",
        "owner_email": "<EMAIL>",
        "owner_password": "securepass123"
    }
    tenant = TenantCreate(**valid_data)
    assert tenant.domain == "test.example.com"
    
    # Invalid domain
    invalid_data = valid_data.copy()
    invalid_data["domain"] = "invalid..domain"
    
    with pytest.raises(ValueError, match="Invalid domain format"):
        TenantCreate(**invalid_data)
```

## 🎯 Definition of Done
- [ ] All tenant schemas are implemented
- [ ] Validation rules work correctly for all fields
- [ ] Slug and domain validation prevents invalid input
- [ ] Owner user fields are properly validated
- [ ] Settings schema supports flexible JSONB structure
- [ ] Error messages are clear and helpful
- [ ] All tests pass with comprehensive coverage
- [ ] Code review is completed
- [ ] Integration with API endpoints is verified
