# Task 1.3: Create Authentication Dependencies

## 📋 Task Overview
**User Story:** HU-2.1 - JWT Authentication System  
**Task ID:** 1.3  
**Estimated Time:** 2 hours  
**Priority:** Critical  
**Complexity:** Medium  

## 🎯 Description
Implement FastAPI dependencies for authentication and authorization that will be used across all protected endpoints. These dependencies handle JWT token extraction, validation, and user context setup.

## 📦 Deliverables
- [ ] Create `backend/src/core/deps.py`
- [ ] Current user dependency with JWT validation
- [ ] Active user dependency
- [ ] Admin user dependency
- [ ] Current tenant dependency
- [ ] Proper error handling for unauthorized access

## ✅ Acceptance Criteria
- [ ] get_current_user extracts and validates JW<PERSON> token
- [ ] Tenant context is set for RLS during user lookup
- [ ] get_current_active_user checks user is active
- [ ] get_current_admin_user enforces admin role
- [ ] get_current_tenant retrieves user's tenant
- [ ] Proper HTTP exceptions for authentication failures
- [ ] Dependencies work with FastAPI dependency injection

## 🔧 Technical Requirements

### Dependencies Structure
```python
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import H<PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from jose import JWTError

from .database import get_db
from .security import verify_token
from .tenant_context import tenant_context
from ..models.user import User
from ..models.tenant import Tenant
```

### Security Scheme
- Use HTTPBearer for token extraction
- Support Authorization header with Bearer token
- Proper error responses for missing/invalid tokens

### Tenant Context Integration
- Set tenant context for RLS before database queries
- Ensure proper tenant isolation
- Handle cross-tenant access prevention

## 🔗 Dependencies
- **Prerequisite:** Security utilities (Task 1.2) must be completed
- **Database:** Database session dependency must exist
- **Models:** User and Tenant models must be available
- **RLS:** Tenant context system must be implemented

## 🧪 Testing Requirements
- [ ] Test token extraction from Authorization header
- [ ] Test JWT token validation
- [ ] Test user lookup with tenant context
- [ ] Test active user validation
- [ ] Test admin role enforcement
- [ ] Test tenant retrieval
- [ ] Test error handling for invalid tokens
- [ ] Test error handling for inactive users

## 📊 Validation Checklist
- [ ] Dependencies integrate properly with FastAPI
- [ ] Error responses follow HTTP standards
- [ ] Tenant context is properly set
- [ ] Performance is acceptable (<50ms per dependency)
- [ ] Type hints are complete
- [ ] Error messages are helpful but secure

## 🚨 Security Considerations
- [ ] Validate JWT tokens completely
- [ ] Ensure tenant isolation is maintained
- [ ] Prevent information disclosure in error messages
- [ ] Log authentication failures appropriately
- [ ] Handle edge cases securely
- [ ] Protect against token replay attacks

## 📈 Performance Considerations
- [ ] Minimize database queries per request
- [ ] Cache user information when appropriate
- [ ] Optimize tenant context setup
- [ ] Avoid N+1 query problems
- [ ] Keep dependency overhead minimal

## 🔄 Implementation Steps
1. **Set up HTTPBearer security scheme**
2. **Implement get_current_user dependency**
3. **Add tenant context integration**
4. **Create get_current_active_user dependency**
5. **Implement get_current_admin_user dependency**
6. **Add get_current_tenant dependency**
7. **Implement comprehensive error handling**
8. **Write unit tests for all dependencies**

## 📝 Code Example
```python
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from jose import JWTError

from .database import get_db
from .security import verify_token
from .tenant_context import tenant_context
from ..models.user import User
from ..models.tenant import Tenant

security = HTTPBearer()

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Get current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = verify_token(credentials.credentials)
        user_id: str = payload.get("sub")
        tenant_id: str = payload.get("tenant_id")
        
        if user_id is None or tenant_id is None:
            raise credentials_exception
            
    except JWTError:
        raise credentials_exception
    
    # Set tenant context for RLS
    with tenant_context(db, tenant_id, user_id):
        user = db.query(User).filter(User.id == user_id).first()
        if user is None:
            raise credentials_exception
        return user

async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

async def get_current_admin_user(
    current_user: User = Depends(get_current_active_user)
) -> User:
    """Get current admin user"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user

async def get_current_tenant(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Tenant:
    """Get current user's tenant"""
    tenant = db.query(Tenant).filter(Tenant.id == current_user.tenant_id).first()
    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )
    return tenant
```

## 🔍 Usage Examples
```python
# In API endpoints
@router.get("/profile")
async def get_profile(current_user: User = Depends(get_current_active_user)):
    return current_user

@router.get("/admin/users")
async def get_users(admin_user: User = Depends(get_current_admin_user)):
    # Only admin users can access this endpoint
    pass

@router.get("/tenant/settings")
async def get_tenant_settings(
    current_tenant: Tenant = Depends(get_current_tenant)
):
    return current_tenant.settings
```

## 🧪 Test Examples
```python
def test_get_current_user_valid_token(test_db, test_user):
    """Test getting current user with valid token"""
    # Create valid JWT token
    token_data = {
        "sub": test_user.id,
        "tenant_id": test_user.tenant_id,
        "role": test_user.role,
        "email": test_user.email
    }
    token = create_access_token(token_data)
    
    # Create credentials
    credentials = HTTPAuthorizationCredentials(
        scheme="Bearer",
        credentials=token
    )
    
    # Test dependency
    user = await get_current_user(credentials, test_db)
    assert user.id == test_user.id

def test_get_current_user_invalid_token(test_db):
    """Test getting current user with invalid token"""
    credentials = HTTPAuthorizationCredentials(
        scheme="Bearer",
        credentials="invalid_token"
    )
    
    with pytest.raises(HTTPException) as exc_info:
        await get_current_user(credentials, test_db)
    
    assert exc_info.value.status_code == 401
```

## 🎯 Definition of Done
- [ ] All authentication dependencies are implemented
- [ ] JWT token validation works correctly
- [ ] Tenant context is properly set
- [ ] User and admin role validation works
- [ ] Tenant retrieval works correctly
- [ ] Error handling is comprehensive
- [ ] All tests pass
- [ ] Performance requirements are met
- [ ] Code review is completed
- [ ] Integration with FastAPI is verified
