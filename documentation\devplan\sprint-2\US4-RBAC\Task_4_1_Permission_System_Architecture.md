# Task 4.1: Define Permission System Architecture

## 📋 Task Overview
**User Story:** HU-2.4 - Role-Based Access Control  
**Task ID:** 4.1  
**Estimated Time:** 2 hours  
**Priority:** Critical  
**Complexity:** High  

## 🎯 Description
Design and document the role-based permission system that defines the security architecture for the entire platform, including role hierarchy, permission constants, and extensible permission checking utilities.

## 📦 Deliverables
- [ ] Create `backend/src/core/permissions.py`
- [ ] Define permission constants and enums
- [ ] Document role hierarchy and inheritance
- [ ] Create permission checking utilities
- [ ] Design extensible permission system

## ✅ Acceptance Criteria
- [ ] Three primary roles defined: Owner, Admin, User
- [ ] Permission hierarchy: Owner > Admin > User
- [ ] Permission constants for all system operations
- [ ] Role-based permission checking functions
- [ ] Extensible design for future custom permissions
- [ ] Clear documentation of permission model
- [ ] Permission inheritance rules documented

## 🔧 Technical Requirements

### Role Hierarchy
```
Owner (System-wide)
├── Can manage all tenants
├── Can create/delete tenants
├── Can manage system settings
└── Inherits all Admin permissions

Admin (Tenant-wide)
├── Can manage tenant users
├── Can manage tenant settings
├── Can view tenant analytics
└── Inherits all User permissions

User (Basic access)
├── Can view assigned content
├── Can update own profile
└── Can access basic features
```

### Permission Categories
1. **System Permissions** (Owner only)
2. **Tenant Permissions** (Admin and above)
3. **User Permissions** (All roles)
4. **Resource Permissions** (Context-dependent)

### Extensibility Requirements
- Support for custom roles in the future
- Granular permission assignment
- Context-aware permissions
- Performance-optimized checking

## 🔗 Dependencies
- **Prerequisite:** User model with role field
- **Standards:** Security best practices for RBAC
- **Future:** Custom role system design considerations

## 🧪 Testing Requirements
- [ ] Test role hierarchy validation
- [ ] Test permission inheritance
- [ ] Test permission checking functions
- [ ] Test extensibility mechanisms
- [ ] Test performance of permission checks
- [ ] Test edge cases and error handling
- [ ] Test documentation completeness

## 📊 Validation Checklist
- [ ] Role hierarchy is clearly defined
- [ ] Permission constants cover all operations
- [ ] Permission checking is efficient (<10ms)
- [ ] Inheritance rules work correctly
- [ ] Extensibility design is sound
- [ ] Documentation is comprehensive
- [ ] Code follows security best practices

## 🚨 Security Considerations
- [ ] Prevent privilege escalation
- [ ] Ensure default-deny security model
- [ ] Validate all permission checks
- [ ] Log permission-related activities
- [ ] Protect against bypass attempts
- [ ] Implement fail-safe defaults

## 📈 Performance Considerations
- [ ] Permission checking overhead <10ms
- [ ] Cache permission results when appropriate
- [ ] Optimize database queries for roles
- [ ] Minimize permission check frequency
- [ ] Use efficient data structures

## 🔄 Implementation Steps
1. **Define role constants and enums**
2. **Create permission constants**
3. **Implement role hierarchy logic**
4. **Create permission checking utilities**
5. **Add inheritance mechanisms**
6. **Design extensibility framework**
7. **Write comprehensive documentation**
8. **Create unit tests**

## 📝 Code Example
```python
from enum import Enum
from typing import List, Set, Optional, Dict, Any
from functools import wraps
from fastapi import HTTPException, status

class Role(str, Enum):
    """User roles with hierarchy"""
    OWNER = "owner"
    ADMIN = "admin"
    USER = "user"

class Permission(str, Enum):
    """System permissions"""
    # System-level permissions (Owner only)
    SYSTEM_MANAGE_TENANTS = "system:manage_tenants"
    SYSTEM_VIEW_ALL_TENANTS = "system:view_all_tenants"
    SYSTEM_MANAGE_SETTINGS = "system:manage_settings"
    
    # Tenant-level permissions (Admin and above)
    TENANT_MANAGE_USERS = "tenant:manage_users"
    TENANT_MANAGE_SETTINGS = "tenant:manage_settings"
    TENANT_VIEW_ANALYTICS = "tenant:view_analytics"
    TENANT_MANAGE_INVITATIONS = "tenant:manage_invitations"
    
    # User-level permissions (All roles)
    USER_VIEW_PROFILE = "user:view_profile"
    USER_UPDATE_PROFILE = "user:update_profile"
    USER_VIEW_CONTENT = "user:view_content"
    
    # Resource-specific permissions
    COURSE_CREATE = "course:create"
    COURSE_EDIT = "course:edit"
    COURSE_DELETE = "course:delete"
    COURSE_VIEW = "course:view"

class PermissionSystem:
    """Central permission management system"""
    
    # Role hierarchy definition
    ROLE_HIERARCHY = {
        Role.OWNER: [Role.ADMIN, Role.USER],
        Role.ADMIN: [Role.USER],
        Role.USER: []
    }
    
    # Permission mappings for each role
    ROLE_PERMISSIONS = {
        Role.OWNER: {
            # System permissions
            Permission.SYSTEM_MANAGE_TENANTS,
            Permission.SYSTEM_VIEW_ALL_TENANTS,
            Permission.SYSTEM_MANAGE_SETTINGS,
            # Inherits all admin and user permissions
        },
        Role.ADMIN: {
            # Tenant permissions
            Permission.TENANT_MANAGE_USERS,
            Permission.TENANT_MANAGE_SETTINGS,
            Permission.TENANT_VIEW_ANALYTICS,
            Permission.TENANT_MANAGE_INVITATIONS,
            # Inherits all user permissions
        },
        Role.USER: {
            # Basic user permissions
            Permission.USER_VIEW_PROFILE,
            Permission.USER_UPDATE_PROFILE,
            Permission.USER_VIEW_CONTENT,
            Permission.COURSE_VIEW,
        }
    }
    
    @classmethod
    def get_role_permissions(cls, role: Role) -> Set[Permission]:
        """Get all permissions for a role including inherited ones"""
        permissions = set(cls.ROLE_PERMISSIONS.get(role, set()))
        
        # Add inherited permissions
        for inherited_role in cls.ROLE_HIERARCHY.get(role, []):
            permissions.update(cls.get_role_permissions(inherited_role))
        
        return permissions
    
    @classmethod
    def has_permission(cls, user_role: Role, required_permission: Permission) -> bool:
        """Check if a role has a specific permission"""
        role_permissions = cls.get_role_permissions(user_role)
        return required_permission in role_permissions
    
    @classmethod
    def has_role_or_higher(cls, user_role: Role, required_role: Role) -> bool:
        """Check if user role is equal to or higher than required role"""
        if user_role == required_role:
            return True
        
        # Check if user role inherits from required role
        inherited_roles = cls.ROLE_HIERARCHY.get(user_role, [])
        return required_role in inherited_roles or any(
            cls.has_role_or_higher(inherited_role, required_role)
            for inherited_role in inherited_roles
        )
    
    @classmethod
    def can_assign_role(cls, assigner_role: Role, target_role: Role) -> bool:
        """Check if a user can assign a specific role to another user"""
        # Users can only assign roles lower than their own
        return cls.has_role_or_higher(assigner_role, target_role) and assigner_role != target_role

# Permission checking decorators
def require_permission(permission: Permission):
    """Decorator to require specific permission"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current user from dependencies
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            user_role = Role(current_user.role)
            if not PermissionSystem.has_permission(user_role, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission {permission.value} required"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def require_role(role: Role):
    """Decorator to require specific role or higher"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            user_role = Role(current_user.role)
            if not PermissionSystem.has_role_or_higher(user_role, role):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Role {role.value} or higher required"
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

# Context-aware permission checking
class PermissionContext:
    """Context for resource-specific permission checking"""
    
    def __init__(self, user_role: Role, tenant_id: str, resource_id: Optional[str] = None):
        self.user_role = user_role
        self.tenant_id = tenant_id
        self.resource_id = resource_id
    
    def can_access_tenant(self, target_tenant_id: str) -> bool:
        """Check if user can access specific tenant"""
        if self.user_role == Role.OWNER:
            return True  # Owners can access all tenants
        
        return self.tenant_id == target_tenant_id
    
    def can_manage_user(self, target_user_role: Role) -> bool:
        """Check if user can manage another user"""
        if not PermissionSystem.has_permission(self.user_role, Permission.TENANT_MANAGE_USERS):
            return False
        
        return PermissionSystem.can_assign_role(self.user_role, target_user_role)

# Utility functions
def get_user_permissions(user_role: str) -> List[str]:
    """Get list of permissions for a user role"""
    role = Role(user_role)
    permissions = PermissionSystem.get_role_permissions(role)
    return [perm.value for perm in permissions]

def validate_role_assignment(assigner_role: str, target_role: str) -> bool:
    """Validate if role assignment is allowed"""
    assigner = Role(assigner_role)
    target = Role(target_role)
    return PermissionSystem.can_assign_role(assigner, target)
```

## 🔍 Usage Examples
```python
# Using permission decorators
@router.post("/users")
@require_permission(Permission.TENANT_MANAGE_USERS)
async def create_user(user_data: UserCreate, current_user: User = Depends(get_current_user)):
    # Only users with TENANT_MANAGE_USERS permission can access
    pass

@router.get("/admin/tenants")
@require_role(Role.OWNER)
async def get_all_tenants(current_user: User = Depends(get_current_user)):
    # Only owners can access
    pass

# Context-aware checking
def check_user_access(current_user: User, target_tenant_id: str):
    context = PermissionContext(
        user_role=Role(current_user.role),
        tenant_id=current_user.tenant_id
    )
    
    if not context.can_access_tenant(target_tenant_id):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Cannot access this tenant"
        )
```

## 🧪 Test Examples
```python
def test_role_hierarchy():
    """Test role hierarchy and inheritance"""
    # Owner should have admin and user permissions
    owner_perms = PermissionSystem.get_role_permissions(Role.OWNER)
    assert Permission.TENANT_MANAGE_USERS in owner_perms
    assert Permission.USER_VIEW_PROFILE in owner_perms
    
    # Admin should have user permissions but not owner permissions
    admin_perms = PermissionSystem.get_role_permissions(Role.ADMIN)
    assert Permission.USER_VIEW_PROFILE in admin_perms
    assert Permission.SYSTEM_MANAGE_TENANTS not in admin_perms

def test_permission_checking():
    """Test permission checking logic"""
    assert PermissionSystem.has_permission(Role.ADMIN, Permission.TENANT_MANAGE_USERS)
    assert not PermissionSystem.has_permission(Role.USER, Permission.TENANT_MANAGE_USERS)
    assert PermissionSystem.has_permission(Role.OWNER, Permission.SYSTEM_MANAGE_TENANTS)

def test_role_assignment_validation():
    """Test role assignment validation"""
    assert PermissionSystem.can_assign_role(Role.OWNER, Role.ADMIN)
    assert PermissionSystem.can_assign_role(Role.ADMIN, Role.USER)
    assert not PermissionSystem.can_assign_role(Role.USER, Role.ADMIN)
    assert not PermissionSystem.can_assign_role(Role.ADMIN, Role.OWNER)
```

## 🎯 Definition of Done
- [ ] Permission system architecture is fully defined
- [ ] Role hierarchy is implemented and tested
- [ ] Permission constants cover all system operations
- [ ] Permission checking utilities work correctly
- [ ] Inheritance mechanisms function properly
- [ ] Extensibility framework is in place
- [ ] Documentation is comprehensive
- [ ] All tests pass with full coverage
- [ ] Performance requirements are met
- [ ] Security review is completed
