"""
Complete Database Integration Test
Comprehensive test for all database integration fixes
"""

import pytest
import requests
import uuid
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os


class TestCompleteDatabaseIntegration:
    """Test complete database integration with all fixes."""

    @pytest.fixture(scope="class")
    def db_engine(self):
        """Create database engine for testing."""
        TEST_DATABASE_URL = os.getenv(
            "TEST_DATABASE_URL", 
            "postgresql://postgres:postgres123@localhost:5432/arroyo_university"
        )
        try:
            engine = create_engine(TEST_DATABASE_URL, echo=False)
            # Test connection
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            yield engine
            engine.dispose()
        except Exception as e:
            pytest.skip(f"Database not available: {e}")

    @pytest.fixture
    def db_session(self, db_engine):
        """Create database session for testing."""
        Session = sessionmaker(bind=db_engine)
        session = Session()
        try:
            yield session
        finally:
            session.rollback()
            session.close()

    @pytest.fixture
    def api_base_url(self):
        """Base URL for API testing."""
        return "http://localhost:8000"

    def test_database_rls_functions_complete(self, db_session):
        """Test that all RLS helper functions are working."""
        # Test all RLS helper functions
        functions_to_test = [
            ("current_tenant_id", "test-tenant-123"),
            ("current_user_id", "test-user-456"),
        ]
        
        for func_name, test_value in functions_to_test:
            # Set the value
            if func_name == "current_tenant_id":
                db_session.execute(text("SELECT set_config('app.current_tenant_id', :value, false)"), 
                                 {'value': test_value})
            else:
                db_session.execute(text("SELECT set_config('app.current_user_id', :value, false)"), 
                                 {'value': test_value})
            
            # Get the value
            result = db_session.execute(text(f"SELECT {func_name}()")).scalar()
            assert result == test_value, f"Function {func_name} not working correctly"

    def test_tenant_context_management_complete(self, db_session):
        """Test complete tenant context management workflow."""
        tenant_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        
        # Test setting tenant context
        db_session.execute(text("SELECT set_config('app.current_tenant_id', :tenant_id, false)"), 
                          {'tenant_id': tenant_id})
        db_session.execute(text("SELECT set_config('app.current_user_id', :user_id, false)"), 
                          {'user_id': user_id})
        
        # Verify context is set
        current_tenant = db_session.execute(text("SELECT current_tenant_id()")).scalar()
        current_user = db_session.execute(text("SELECT current_user_id()")).scalar()
        
        assert current_tenant == tenant_id
        assert current_user == user_id
        
        # Test clearing context
        db_session.execute(text("SELECT set_config('app.current_tenant_id', '', false)"))
        db_session.execute(text("SELECT set_config('app.current_user_id', '', false)"))
        
        # Verify context is cleared
        current_tenant = db_session.execute(text("SELECT current_tenant_id()")).scalar()
        current_user = db_session.execute(text("SELECT current_user_id()")).scalar()
        
        assert current_tenant == '' or current_tenant is None
        assert current_user == '' or current_user is None

    def test_rls_policies_enforcement(self, db_session):
        """Test that RLS policies are properly enforcing tenant isolation."""
        # Create two test tenants
        tenant1_id = str(uuid.uuid4())
        tenant2_id = str(uuid.uuid4())
        
        # Create tenants
        for tenant_id, name in [(tenant1_id, 'Tenant 1'), (tenant2_id, 'Tenant 2')]:
            db_session.execute(text("""
                INSERT INTO tenants (tenant_id, name, subdomain, status)
                VALUES (:tenant_id, :name, :subdomain, 'active')
                ON CONFLICT (tenant_id) DO NOTHING
            """), {
                'tenant_id': tenant_id, 
                'name': name, 
                'subdomain': f'tenant-{tenant_id[:8]}'
            })
        db_session.commit()
        
        # Create users in each tenant
        user1_id = str(uuid.uuid4())
        user2_id = str(uuid.uuid4())
        
        for user_id, tenant_id, email in [
            (user1_id, tenant1_id, f'user1-{user1_id[:8]}@tenant1.com'),
            (user2_id, tenant2_id, f'user2-{user2_id[:8]}@tenant2.com')
        ]:
            db_session.execute(text("""
                INSERT INTO users (user_id, tenant_id, email, username, status)
                VALUES (:user_id, :tenant_id, :email, :username, 'active')
                ON CONFLICT (user_id) DO NOTHING
            """), {
                'user_id': user_id, 
                'tenant_id': tenant_id, 
                'email': email, 
                'username': email.split('@')[0]
            })
        db_session.commit()
        
        # Test tenant 1 context - should only see tenant 1 users
        db_session.execute(text("SELECT set_config('app.current_tenant_id', :tenant_id, false)"), 
                          {'tenant_id': tenant1_id})
        
        users = db_session.execute(text("SELECT * FROM users WHERE tenant_id = :tenant_id"), 
                                  {'tenant_id': tenant1_id}).fetchall()
        tenant1_user_count = len(users)
        assert tenant1_user_count >= 1, "Should see at least the test user for tenant 1"
        
        # Test tenant 2 context - should only see tenant 2 users
        db_session.execute(text("SELECT set_config('app.current_tenant_id', :tenant_id, false)"), 
                          {'tenant_id': tenant2_id})
        
        users = db_session.execute(text("SELECT * FROM users WHERE tenant_id = :tenant_id"), 
                                  {'tenant_id': tenant2_id}).fetchall()
        tenant2_user_count = len(users)
        assert tenant2_user_count >= 1, "Should see at least the test user for tenant 2"
        
        # Clean up
        db_session.execute(text("DELETE FROM users WHERE user_id IN (:user1_id, :user2_id)"), 
                          {'user1_id': user1_id, 'user2_id': user2_id})
        db_session.execute(text("DELETE FROM tenants WHERE tenant_id IN (:tenant1_id, :tenant2_id)"), 
                          {'tenant1_id': tenant1_id, 'tenant2_id': tenant2_id})
        db_session.commit()

    def test_api_with_database_integration(self, api_base_url):
        """Test that API works correctly with database integration."""
        try:
            # Test health endpoint (which likely involves database checks)
            response = requests.get(f"{api_base_url}/health", timeout=10)
            assert response.status_code == 200
            
            data = response.json()
            assert data["status"] == "healthy"
            assert data["service"] == "core-api"
            
            # Test with tenant headers
            headers = {"X-Tenant-ID": "test-tenant-api"}
            response = requests.get(f"{api_base_url}/health", headers=headers, timeout=10)
            assert response.status_code == 200
            
        except requests.exceptions.RequestException as e:
            pytest.skip(f"API not available: {e}")

    def test_complete_database_integration_workflow(self, db_session, api_base_url):
        """Test complete database integration workflow."""
        # 1. Test database connectivity
        assert db_session.execute(text("SELECT 1")).scalar() == 1
        
        # 2. Test RLS functions are available
        db_session.execute(text("SELECT set_config('app.current_tenant_id', 'workflow-test', false)"))
        tenant_id = db_session.execute(text("SELECT current_tenant_id()")).scalar()
        assert tenant_id == 'workflow-test'
        
        # 3. Test table structure
        tables = db_session.execute(text("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name IN ('tenants', 'users', 'courses')
        """)).fetchall()
        table_names = [t[0] for t in tables]
        assert 'tenants' in table_names
        assert 'users' in table_names
        
        # 4. Test RLS is enabled
        rls_tables = db_session.execute(text("""
            SELECT relname FROM pg_class 
            WHERE relname IN ('users', 'tenants') AND relrowsecurity = true
        """)).fetchall()
        assert len(rls_tables) >= 1, "RLS should be enabled on key tables"
        
        # 5. Test API connectivity
        try:
            response = requests.get(f"{api_base_url}/health", timeout=5)
            assert response.status_code == 200
            api_working = True
        except:
            api_working = False
        
        # 6. Test tenant context with API (if available)
        if api_working:
            headers = {"X-Tenant-ID": "workflow-test-api"}
            response = requests.get(f"{api_base_url}/health", headers=headers, timeout=5)
            assert response.status_code == 200
        
        print("✅ Complete database integration workflow test passed!")
        print(f"   - Database connectivity: ✅")
        print(f"   - RLS helper functions: ✅")
        print(f"   - Table structure: ✅")
        print(f"   - RLS policies: ✅")
        print(f"   - API integration: {'✅' if api_working else '⚠️ (skipped)'}")
        print(f"   - Tenant context: ✅")

    def test_database_integration_performance(self, db_session):
        """Test that database integration doesn't impact performance significantly."""
        import time
        
        # Test basic query performance
        start_time = time.time()
        for _ in range(10):
            db_session.execute(text("SELECT 1")).scalar()
        basic_time = time.time() - start_time
        
        # Test RLS function performance
        start_time = time.time()
        for _ in range(10):
            db_session.execute(text("SELECT set_config('app.current_tenant_id', 'perf-test', false)"))
            db_session.execute(text("SELECT current_tenant_id()")).scalar()
        rls_time = time.time() - start_time
        
        # Performance should be reasonable
        assert basic_time < 1.0, f"Basic queries took {basic_time:.2f}s for 10 queries"
        assert rls_time < 2.0, f"RLS operations took {rls_time:.2f}s for 10 operations"
        
        print(f"✅ Performance test passed!")
        print(f"   - Basic queries: {basic_time:.3f}s for 10 queries")
        print(f"   - RLS operations: {rls_time:.3f}s for 10 operations")

    def test_database_integration_error_handling(self, db_session):
        """Test that database integration handles errors gracefully."""
        # Test invalid tenant ID
        try:
            db_session.execute(text("SELECT set_config('app.current_tenant_id', 'invalid-tenant', false)"))
            result = db_session.execute(text("SELECT current_tenant_id()")).scalar()
            assert result == 'invalid-tenant'  # Should work even with invalid tenant
        except Exception as e:
            pytest.fail(f"Should handle invalid tenant ID gracefully: {e}")
        
        # Test empty tenant ID
        try:
            db_session.execute(text("SELECT set_config('app.current_tenant_id', '', false)"))
            result = db_session.execute(text("SELECT current_tenant_id()")).scalar()
            assert result == '' or result is None
        except Exception as e:
            pytest.fail(f"Should handle empty tenant ID gracefully: {e}")
        
        # Test malformed queries (should fail gracefully)
        with pytest.raises(Exception):
            db_session.execute(text("SELECT invalid_function()"))

    def test_database_integration_complete_validation(self, db_session, api_base_url):
        """Complete validation of all database integration fixes."""
        validation_results = {
            "database_connectivity": False,
            "rls_functions": False,
            "tenant_context": False,
            "table_structure": False,
            "rls_policies": False,
            "api_integration": False,
            "middleware_integration": False,
            "performance": False,
            "error_handling": False
        }
        
        try:
            # 1. Database connectivity
            db_session.execute(text("SELECT 1")).scalar()
            validation_results["database_connectivity"] = True
            
            # 2. RLS functions
            db_session.execute(text("SELECT set_config('app.current_tenant_id', 'validation', false)"))
            result = db_session.execute(text("SELECT current_tenant_id()")).scalar()
            validation_results["rls_functions"] = (result == 'validation')
            
            # 3. Tenant context
            db_session.execute(text("SELECT set_config('app.current_user_id', 'test-user', false)"))
            result = db_session.execute(text("SELECT current_user_id()")).scalar()
            validation_results["tenant_context"] = (result == 'test-user')
            
            # 4. Table structure
            tables = db_session.execute(text("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name IN ('tenants', 'users')
            """)).scalar()
            validation_results["table_structure"] = (tables >= 2)
            
            # 5. RLS policies
            policies = db_session.execute(text("""
                SELECT COUNT(*) FROM pg_policies WHERE tablename = 'users'
            """)).scalar()
            validation_results["rls_policies"] = (policies > 0)
            
            # 6. API integration
            try:
                response = requests.get(f"{api_base_url}/health", timeout=5)
                validation_results["api_integration"] = (response.status_code == 200)
            except:
                pass
            
            # 7. Middleware integration
            try:
                headers = {"X-Tenant-ID": "validation-test"}
                response = requests.get(f"{api_base_url}/health", headers=headers, timeout=5)
                validation_results["middleware_integration"] = (response.status_code == 200)
            except:
                pass
            
            # 8. Performance
            import time
            start = time.time()
            for _ in range(5):
                db_session.execute(text("SELECT current_tenant_id()")).scalar()
            duration = time.time() - start
            validation_results["performance"] = (duration < 1.0)
            
            # 9. Error handling
            try:
                db_session.execute(text("SELECT set_config('app.current_tenant_id', '', false)"))
                validation_results["error_handling"] = True
            except:
                pass
            
        except Exception as e:
            print(f"Validation error: {e}")
        
        # Report results
        print("\n🔍 Database Integration Validation Results:")
        for check, passed in validation_results.items():
            status = "✅" if passed else "❌"
            print(f"   - {check.replace('_', ' ').title()}: {status}")
        
        # Overall validation
        passed_checks = sum(validation_results.values())
        total_checks = len(validation_results)
        
        print(f"\n📊 Overall Result: {passed_checks}/{total_checks} checks passed")
        
        # Require at least 7/9 checks to pass (API might not be available in some test environments)
        assert passed_checks >= 7, f"Database integration validation failed: only {passed_checks}/{total_checks} checks passed"
        
        return validation_results
