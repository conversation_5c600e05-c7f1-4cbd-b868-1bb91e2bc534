# Task 1.2: Implement Security Utilities

## 📋 Task Overview
**User Story:** HU-2.1 - JWT Authentication System  
**Task ID:** 1.2  
**Estimated Time:** 3 hours  
**Priority:** Critical  
**Complexity:** High  

## 🎯 Description
Create JWT token generation, validation, and password hashing utilities that form the security foundation of the authentication system. These utilities must be secure, performant, and configurable.

## 📦 Deliverables
- [ ] Create `backend/src/core/security.py`
- [ ] Password hashing with bcrypt (12+ rounds)
- [ ] JWT token creation and validation
- [ ] Secure token generation for resets
- [ ] Error handling for invalid tokens

## ✅ Acceptance Criteria
- [ ] Password hashing uses bcrypt with minimum 12 rounds
- [ ] JWT tokens include user_id, tenant_id, role, email in payload
- [ ] Token expiration is configurable via environment variables
- [ ] Token validation includes proper error handling
- [ ] Secure random token generation for password resets
- [ ] All functions have proper type hints and documentation

## 🔧 Technical Requirements

### Dependencies
```python
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
import secrets
```

### Password Security
- Use bcrypt with minimum 12 rounds
- Salt is automatically handled by bcrypt
- Secure password verification
- Protection against timing attacks

### JWT Token Structure
```json
{
  "sub": "user_id",
  "tenant_id": "tenant_uuid",
  "role": "admin",
  "email": "<EMAIL>",
  "exp": 1234567890,
  "iat": 1234567890
}
```

### Configuration Requirements
- `SECRET_KEY`: JWT signing key (minimum 32 characters)
- `ALGORITHM`: JWT algorithm (default: HS256)
- `ACCESS_TOKEN_EXPIRE_MINUTES`: Token expiration (default: 1440 = 24 hours)

## 🔗 Dependencies
- **Prerequisite:** Configuration system must be set up
- **Libraries:** python-jose[cryptography], passlib[bcrypt]
- **Environment:** SECRET_KEY and other auth variables configured

## 🧪 Testing Requirements
- [ ] Test password hashing and verification
- [ ] Test JWT token creation with all required claims
- [ ] Test JWT token validation and expiration
- [ ] Test invalid token handling
- [ ] Test secure token generation
- [ ] Performance test password hashing time
- [ ] Security test against common attacks

## 📊 Validation Checklist
- [ ] Password hashing takes 50-150ms (security vs performance)
- [ ] JWT tokens are properly signed and verifiable
- [ ] Token expiration works correctly
- [ ] Error handling is comprehensive
- [ ] Type hints are complete
- [ ] Documentation is clear and complete

## 🚨 Security Considerations
- [ ] Use cryptographically secure random number generation
- [ ] Protect against timing attacks in password verification
- [ ] Ensure JWT secret key is sufficiently random and long
- [ ] Validate all JWT claims properly
- [ ] Handle token expiration gracefully
- [ ] Log security events appropriately

## 📈 Performance Considerations
- [ ] Password hashing should take 50-150ms
- [ ] JWT token validation should be <10ms
- [ ] Token generation should be <5ms
- [ ] Memory usage should be minimal
- [ ] No blocking operations in token validation

## 🔄 Implementation Steps
1. **Set up password context** with bcrypt configuration
2. **Implement password hashing** and verification functions
3. **Create JWT token generation** with proper claims
4. **Implement JWT validation** with error handling
5. **Add secure token generation** for resets
6. **Add comprehensive error handling**
7. **Write unit tests** for all functions
8. **Performance and security testing**

## 📝 Code Example
```python
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
import secrets

from .config import settings

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash a password"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a JWT access token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire, "iat": datetime.utcnow()})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> dict:
    """Verify and decode a JWT token"""
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

def generate_reset_token() -> str:
    """Generate a secure reset token"""
    return secrets.token_urlsafe(32)

def generate_verification_token() -> str:
    """Generate a secure email verification token"""
    return secrets.token_urlsafe(32)
```

## 🔍 Security Testing
```python
def test_password_security():
    """Test password hashing security"""
    password = "test_password_123"
    
    # Test hashing
    hash1 = get_password_hash(password)
    hash2 = get_password_hash(password)
    
    # Hashes should be different (salt)
    assert hash1 != hash2
    
    # Both should verify correctly
    assert verify_password(password, hash1)
    assert verify_password(password, hash2)
    
    # Wrong password should fail
    assert not verify_password("wrong_password", hash1)

def test_jwt_security():
    """Test JWT token security"""
    data = {
        "sub": "user_id",
        "tenant_id": "tenant_id",
        "role": "admin",
        "email": "<EMAIL>"
    }
    
    # Create token
    token = create_access_token(data)
    
    # Verify token
    payload = verify_token(token)
    
    # Check all claims
    assert payload["sub"] == data["sub"]
    assert payload["tenant_id"] == data["tenant_id"]
    assert payload["role"] == data["role"]
    assert payload["email"] == data["email"]
    assert "exp" in payload
    assert "iat" in payload
```

## 🎯 Definition of Done
- [ ] All security functions are implemented and tested
- [ ] Password hashing uses bcrypt with 12+ rounds
- [ ] JWT tokens include all required claims
- [ ] Token validation handles all error cases
- [ ] Secure token generation works correctly
- [ ] Performance requirements are met
- [ ] Security tests pass
- [ ] Code review is completed
- [ ] Documentation is comprehensive
