# Task 6.1: Create Invitation Data Model

## 📋 Task Overview
**User Story:** HU-2.6 - Invitation System  
**Task ID:** 6.1  
**Estimated Time:** 1.5 hours  
**Priority:** High  
**Complexity:** Medium  

## 🎯 Description
Implement the Invitation model with all required fields and relationships that supports secure invitation workflows, token management, and proper tracking of invitation status and usage.

## 📦 Deliverables
- [ ] Create `backend/src/models/invitation.py`
- [ ] Invitation model with security and tracking fields
- [ ] Relationship with User and Tenant models
- [ ] Invitation status and expiration logic
- [ ] Database migration for invitation table

## ✅ Acceptance Criteria
- [ ] Invitation model inherits from Base, UUIDMixin, TimestampMixin, TenantMixin
- [ ] Email, token, role, and expiration fields properly defined
- [ ] Secure token generation (32-byte random)
- [ ] Status tracking (pending, accepted, expired, cancelled)
- [ ] Relationship with Tenant model
- [ ] Helper properties (is_expired, is_valid)
- [ ] Database indexes for performance

## 🔧 Technical Requirements

### Model Structure
```python
class Invitation(Base, UUIDMixin, TimestampMixin, TenantMixin):
    __tablename__ = "invitations"
    
    # Basic invitation info
    email = Column(String(255), nullable=False, index=True)
    token = Column(String(255), unique=True, nullable=False, index=True)
    role = Column(String(50), default="user", nullable=False)
    
    # Status and expiration
    status = Column(String(50), default="pending", nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    
    # Usage tracking
    accepted_at = Column(DateTime(timezone=True), nullable=True)
    accepted_by = Column(String, ForeignKey("users.id"), nullable=True)
    
    # Invitation metadata
    invited_by = Column(String, ForeignKey("users.id"), nullable=False)
    message = Column(String(500), nullable=True)
    metadata = Column(JSON, nullable=True)
```

### Security Requirements
- Cryptographically secure token generation
- Token uniqueness enforcement
- Expiration time validation (default 7 days)
- Status transition validation

### Performance Requirements
- Indexes on email, token, and status fields
- Efficient queries for invitation lookup
- Cleanup of expired invitations

## 🔗 Dependencies
- **Prerequisite:** Base model classes, User and Tenant models
- **Database:** PostgreSQL with proper constraints
- **Security:** Secure token generation utilities

## 📝 Code Example
```python
from sqlalchemy import Column, String, DateTime, Boolean, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime, timedelta
import secrets

from .base import Base, TimestampMixin, UUIDMixin, TenantMixin

class InvitationStatus:
    PENDING = "pending"
    ACCEPTED = "accepted"
    EXPIRED = "expired"
    CANCELLED = "cancelled"

class Invitation(Base, UUIDMixin, TimestampMixin, TenantMixin):
    __tablename__ = "invitations"
    
    # Basic invitation information
    email = Column(String(255), nullable=False, index=True)
    token = Column(String(255), unique=True, nullable=False, index=True)
    role = Column(String(50), default="user", nullable=False)
    
    # Status and timing
    status = Column(String(50), default=InvitationStatus.PENDING, nullable=False, index=True)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    
    # Usage tracking
    accepted_at = Column(DateTime(timezone=True), nullable=True)
    accepted_by = Column(String, ForeignKey("users.id"), nullable=True)
    
    # Invitation context
    invited_by = Column(String, ForeignKey("users.id"), nullable=False)
    message = Column(String(500), nullable=True)
    metadata = Column(JSON, nullable=True)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="invitations")
    inviter = relationship("User", foreign_keys=[invited_by], back_populates="sent_invitations")
    accepter = relationship("User", foreign_keys=[accepted_by], back_populates="accepted_invitations")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if not self.token:
            self.token = self.generate_secure_token()
        if not self.expires_at:
            self.expires_at = datetime.utcnow() + timedelta(days=7)
    
    @staticmethod
    def generate_secure_token() -> str:
        """Generate a cryptographically secure invitation token"""
        return secrets.token_urlsafe(32)
    
    @property
    def is_expired(self) -> bool:
        """Check if invitation has expired"""
        return datetime.utcnow() > self.expires_at
    
    @property
    def is_valid(self) -> bool:
        """Check if invitation is valid for acceptance"""
        return (
            self.status == InvitationStatus.PENDING and
            not self.is_expired
        )
    
    @property
    def is_accepted(self) -> bool:
        """Check if invitation has been accepted"""
        return self.status == InvitationStatus.ACCEPTED
    
    @property
    def is_cancelled(self) -> bool:
        """Check if invitation has been cancelled"""
        return self.status == InvitationStatus.CANCELLED
    
    def accept(self, user_id: str) -> bool:
        """Mark invitation as accepted"""
        if not self.is_valid:
            return False
        
        self.status = InvitationStatus.ACCEPTED
        self.accepted_at = datetime.utcnow()
        self.accepted_by = user_id
        return True
    
    def cancel(self) -> bool:
        """Cancel the invitation"""
        if self.status in [InvitationStatus.ACCEPTED, InvitationStatus.EXPIRED]:
            return False
        
        self.status = InvitationStatus.CANCELLED
        return True
    
    def extend_expiration(self, days: int = 7) -> None:
        """Extend invitation expiration"""
        self.expires_at = datetime.utcnow() + timedelta(days=days)
    
    def regenerate_token(self) -> str:
        """Generate a new token for the invitation"""
        self.token = self.generate_secure_token()
        return self.token
    
    @classmethod
    def cleanup_expired(cls, db_session):
        """Clean up expired invitations"""
        expired_invitations = db_session.query(cls).filter(
            cls.expires_at < datetime.utcnow(),
            cls.status == InvitationStatus.PENDING
        ).all()
        
        for invitation in expired_invitations:
            invitation.status = InvitationStatus.EXPIRED
        
        db_session.commit()
        return len(expired_invitations)
    
    def __repr__(self):
        return f"<Invitation(id={self.id}, email={self.email}, status={self.status})>"

# Add relationships to existing models
# In User model:
# sent_invitations = relationship("Invitation", foreign_keys="Invitation.invited_by", back_populates="inviter")
# accepted_invitations = relationship("Invitation", foreign_keys="Invitation.accepted_by", back_populates="accepter")

# In Tenant model:
# invitations = relationship("Invitation", back_populates="tenant")
```

## 🧪 Testing Requirements
- [ ] Test invitation creation with secure token
- [ ] Test expiration logic and validation
- [ ] Test status transitions (pending -> accepted/cancelled/expired)
- [ ] Test helper properties (is_valid, is_expired, etc.)
- [ ] Test token uniqueness enforcement
- [ ] Test relationship with User and Tenant models
- [ ] Test cleanup of expired invitations
- [ ] Performance test for invitation queries

## 📊 Validation Checklist
- [ ] Model follows project naming conventions
- [ ] All required fields are properly typed
- [ ] Relationships are correctly defined
- [ ] Indexes are optimized for query patterns
- [ ] Helper methods work as expected
- [ ] Security requirements are met
- [ ] Database constraints are properly defined

## 🚨 Security Considerations
- [ ] Token generation is cryptographically secure
- [ ] Token uniqueness is enforced at database level
- [ ] Expiration times are validated
- [ ] Status transitions are controlled
- [ ] No sensitive data in logs or error messages

## 📈 Performance Considerations
- [ ] Email index for invitation lookup
- [ ] Token index for acceptance workflow
- [ ] Status index for filtering
- [ ] Composite indexes for common queries
- [ ] Efficient cleanup of expired invitations

## 🎯 Definition of Done
- [ ] Invitation model is created and tested
- [ ] All fields and relationships work correctly
- [ ] Helper properties function as expected
- [ ] Security requirements are implemented
- [ ] Database indexes are created
- [ ] Model integrates with existing codebase
- [ ] Tests pass with >95% coverage
- [ ] Code review is completed
