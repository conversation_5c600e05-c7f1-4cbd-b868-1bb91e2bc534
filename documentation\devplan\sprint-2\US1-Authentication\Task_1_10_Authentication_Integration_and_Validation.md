# Task 1.10: Authentication Integration and Validation

## 📋 Task Overview
**User Story:** HU-2.1 - JWT Authentication System  
**Task ID:** 1.10  
**Estimated Time:** 2 hours  
**Priority:** Critical  
**Complexity:** Medium  

## 🎯 Description
Ensure all authentication components work together seamlessly by creating integration tests, validation scripts, and end-to-end workflows. This task validates that the complete authentication system is functional and ready for production use.

## 📦 Deliverables
- [ ] Create `backend/scripts/validate_auth_system.py`
- [ ] End-to-end authentication workflow tests
- [ ] Database migration for User table
- [ ] Authentication system integration validation
- [ ] Performance benchmarking script

## ✅ Acceptance Criteria
- [ ] Complete user registration → login → protected endpoint workflow works
- [ ] JWT tokens are properly generated and validated
- [ ] Tenant isolation works correctly in authentication
- [ ] Password hashing and verification work securely
- [ ] All authentication endpoints return correct responses
- [ ] Database constraints and indexes are properly created
- [ ] Performance benchmarks meet requirements (<200ms auth)
- [ ] Security validation passes all checks

## 🔧 Technical Requirements

### Integration Validation
1. **End-to-End Workflow**
   - User registration with validation
   - Login with JWT token generation
   - Protected endpoint access with token
   - Token expiration and refresh

2. **Database Integration**
   - User table creation with proper constraints
   - Indexes for performance optimization
   - Tenant isolation verification

3. **Security Validation**
   - Password hashing strength verification
   - JWT token security validation
   - Authentication bypass prevention

## 🔗 Dependencies
- **Prerequisite:** All Tasks 1.1-1.9 must be completed
- **Database:** PostgreSQL with proper configuration
- **Environment:** All authentication environment variables set

## 📝 Code Example
```python
#!/usr/bin/env python3
"""
Authentication System Validation Script
Validates that all authentication components work together correctly.
"""

import asyncio
import sys
import os
import time
from datetime import datetime, timedelta
import requests
import psycopg2
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the backend src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from core.config import settings
from core.security import verify_password, get_password_hash, create_access_token, verify_token
from core.database import get_db, engine
from models.user import User
from models.tenant import Tenant
from services.auth_service import AuthService
from schemas.auth import UserCreate, LoginRequest

class AuthenticationValidator:
    def __init__(self):
        self.base_url = "http://localhost:8000/api/v1"
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
        print(f"{status} {test_name}: {message}")
        
    def validate_environment(self):
        """Validate environment configuration"""
        print("\n🔧 Validating Environment Configuration...")
        
        required_vars = [
            'SECRET_KEY', 'DATABASE_URL', 'ACCESS_TOKEN_EXPIRE_MINUTES'
        ]
        
        for var in required_vars:
            value = getattr(settings, var, None)
            if not value:
                self.log_test(f"Environment Variable {var}", False, "Missing or empty")
                return False
            else:
                self.log_test(f"Environment Variable {var}", True, "Present")
        
        # Validate SECRET_KEY strength
        if len(settings.SECRET_KEY) < 32:
            self.log_test("SECRET_KEY Strength", False, "Too short (< 32 chars)")
            return False
        else:
            self.log_test("SECRET_KEY Strength", True, f"{len(settings.SECRET_KEY)} characters")
        
        return True
    
    def validate_database_connection(self):
        """Validate database connection and schema"""
        print("\n🗄️ Validating Database Connection...")
        
        try:
            # Test basic connection
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                self.log_test("Database Connection", True, "Connected successfully")
                
            # Check if User table exists
            with engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'users'
                    );
                """))
                table_exists = result.scalar()
                
                if table_exists:
                    self.log_test("User Table Exists", True, "Table found")
                else:
                    self.log_test("User Table Exists", False, "Table not found")
                    return False
                
            # Check indexes
            with engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT indexname FROM pg_indexes 
                    WHERE tablename = 'users' 
                    AND indexname LIKE '%email%';
                """))
                indexes = result.fetchall()
                
                if indexes:
                    self.log_test("Email Index", True, f"Found {len(indexes)} email indexes")
                else:
                    self.log_test("Email Index", False, "No email indexes found")
                    
            return True
            
        except Exception as e:
            self.log_test("Database Connection", False, str(e))
            return False
    
    def validate_security_utilities(self):
        """Validate security utilities"""
        print("\n🔐 Validating Security Utilities...")
        
        # Test password hashing
        test_password = "test_password_123"
        start_time = time.time()
        hashed = get_password_hash(test_password)
        hash_time = (time.time() - start_time) * 1000
        
        if hashed and hashed != test_password:
            self.log_test("Password Hashing", True, f"Hash generated in {hash_time:.2f}ms")
        else:
            self.log_test("Password Hashing", False, "Hash generation failed")
            return False
        
        # Test password verification
        if verify_password(test_password, hashed):
            self.log_test("Password Verification", True, "Correct password verified")
        else:
            self.log_test("Password Verification", False, "Password verification failed")
            return False
        
        # Test wrong password
        if not verify_password("wrong_password", hashed):
            self.log_test("Wrong Password Rejection", True, "Wrong password rejected")
        else:
            self.log_test("Wrong Password Rejection", False, "Wrong password accepted")
            return False
        
        # Test JWT token creation
        test_data = {
            "sub": "test_user_id",
            "tenant_id": "test_tenant_id",
            "role": "user",
            "email": "<EMAIL>"
        }
        
        start_time = time.time()
        token = create_access_token(test_data)
        token_time = (time.time() - start_time) * 1000
        
        if token and len(token) > 100:
            self.log_test("JWT Token Creation", True, f"Token created in {token_time:.2f}ms")
        else:
            self.log_test("JWT Token Creation", False, "Token creation failed")
            return False
        
        # Test JWT token verification
        try:
            start_time = time.time()
            payload = verify_token(token)
            verify_time = (time.time() - start_time) * 1000
            
            if payload["sub"] == test_data["sub"]:
                self.log_test("JWT Token Verification", True, f"Token verified in {verify_time:.2f}ms")
            else:
                self.log_test("JWT Token Verification", False, "Token payload mismatch")
                return False
        except Exception as e:
            self.log_test("JWT Token Verification", False, str(e))
            return False
        
        return True
    
    def validate_auth_service(self):
        """Validate authentication service"""
        print("\n🔧 Validating Authentication Service...")
        
        # Create a test session
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            auth_service = AuthService(db)
            
            # Create a test tenant first (simplified)
            test_tenant = Tenant(
                name="Test Tenant",
                slug="test-tenant",
                domain="test.example.com"
            )
            db.add(test_tenant)
            db.commit()
            db.refresh(test_tenant)
            
            # Test user creation
            user_data = UserCreate(
                email="<EMAIL>",
                password="securepassword123",
                first_name="Test",
                last_name="User",
                role="user",
                tenant_id=test_tenant.id
            )
            
            try:
                user = auth_service.create_user(user_data)
                self.log_test("User Creation", True, f"User created with ID: {user.id}")
            except Exception as e:
                self.log_test("User Creation", False, str(e))
                return False
            
            # Test user authentication
            login_data = LoginRequest(
                email="<EMAIL>",
                password="securepassword123",
                tenant_slug="test-tenant"
            )
            
            authenticated_user = auth_service.authenticate_user(login_data)
            if authenticated_user:
                self.log_test("User Authentication", True, "User authenticated successfully")
            else:
                self.log_test("User Authentication", False, "Authentication failed")
                return False
            
            # Test token creation for user
            token_response = auth_service.create_access_token_for_user(authenticated_user)
            if "access_token" in token_response:
                self.log_test("Token Creation for User", True, "Token created successfully")
            else:
                self.log_test("Token Creation for User", False, "Token creation failed")
                return False
            
            # Cleanup
            db.delete(user)
            db.delete(test_tenant)
            db.commit()
            
            return True
            
        except Exception as e:
            self.log_test("Auth Service Validation", False, str(e))
            return False
        finally:
            db.close()
    
    def validate_api_endpoints(self):
        """Validate API endpoints"""
        print("\n🌐 Validating API Endpoints...")
        
        try:
            # Test health endpoint first
            response = requests.get(f"{self.base_url}/../health", timeout=5)
            if response.status_code == 200:
                self.log_test("API Server Health", True, "Server is running")
            else:
                self.log_test("API Server Health", False, f"Status: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_test("API Server Health", False, str(e))
            return False
        
        # Test authentication endpoints
        try:
            # Test login endpoint structure (should fail without credentials)
            response = requests.post(f"{self.base_url}/auth/login", json={
                "email": "<EMAIL>",
                "password": "wrongpassword"
            }, timeout=5)
            
            if response.status_code == 401:
                self.log_test("Login Endpoint", True, "Returns 401 for invalid credentials")
            else:
                self.log_test("Login Endpoint", False, f"Unexpected status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            self.log_test("Login Endpoint", False, str(e))
            return False
        
        return True
    
    def validate_performance(self):
        """Validate performance requirements"""
        print("\n⚡ Validating Performance...")
        
        # Test password hashing performance
        iterations = 10
        total_time = 0
        
        for _ in range(iterations):
            start_time = time.time()
            get_password_hash("test_password_123")
            total_time += time.time() - start_time
        
        avg_hash_time = (total_time / iterations) * 1000
        if avg_hash_time < 200:  # Should be under 200ms
            self.log_test("Password Hashing Performance", True, f"Avg: {avg_hash_time:.2f}ms")
        else:
            self.log_test("Password Hashing Performance", False, f"Too slow: {avg_hash_time:.2f}ms")
        
        # Test JWT token validation performance
        test_data = {"sub": "test", "tenant_id": "test", "role": "user"}
        token = create_access_token(test_data)
        
        total_time = 0
        for _ in range(iterations):
            start_time = time.time()
            verify_token(token)
            total_time += time.time() - start_time
        
        avg_verify_time = (total_time / iterations) * 1000
        if avg_verify_time < 50:  # Should be under 50ms
            self.log_test("JWT Verification Performance", True, f"Avg: {avg_verify_time:.2f}ms")
        else:
            self.log_test("JWT Verification Performance", False, f"Too slow: {avg_verify_time:.2f}ms")
        
        return True
    
    def run_validation(self):
        """Run complete validation suite"""
        print("🚀 Starting Authentication System Validation\n")
        
        validations = [
            self.validate_environment,
            self.validate_database_connection,
            self.validate_security_utilities,
            self.validate_auth_service,
            self.validate_api_endpoints,
            self.validate_performance
        ]
        
        all_passed = True
        for validation in validations:
            try:
                result = validation()
                if not result:
                    all_passed = False
            except Exception as e:
                print(f"❌ CRITICAL ERROR in {validation.__name__}: {e}")
                all_passed = False
        
        # Print summary
        print("\n" + "="*60)
        print("📊 VALIDATION SUMMARY")
        print("="*60)
        
        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)
        
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if all_passed:
            print("\n🎉 ALL VALIDATIONS PASSED!")
            print("✅ Authentication system is ready for use")
        else:
            print("\n⚠️  SOME VALIDATIONS FAILED!")
            print("❌ Please fix the issues before proceeding")
            
            # Show failed tests
            failed_tests = [r for r in self.test_results if not r["success"]]
            if failed_tests:
                print("\nFailed Tests:")
                for test in failed_tests:
                    print(f"  - {test['test']}: {test['message']}")
        
        return all_passed

if __name__ == "__main__":
    validator = AuthenticationValidator()
    success = validator.run_validation()
    sys.exit(0 if success else 1)
```

## 🧪 Testing Requirements
- [ ] Run validation script successfully
- [ ] All authentication components pass integration tests
- [ ] Performance benchmarks meet requirements
- [ ] Security validation passes all checks
- [ ] End-to-end workflow completes successfully

## 📊 Validation Checklist
- [ ] Environment configuration is complete
- [ ] Database schema is properly created
- [ ] Security utilities work correctly
- [ ] Authentication service functions properly
- [ ] API endpoints respond correctly
- [ ] Performance meets requirements
- [ ] All integration points work together

## 🚨 Security Considerations
- [ ] Password hashing strength is validated
- [ ] JWT token security is verified
- [ ] Authentication bypass prevention is tested
- [ ] Tenant isolation is confirmed
- [ ] Error handling doesn't leak information

## 📈 Performance Considerations
- [ ] Authentication response time <200ms
- [ ] Password hashing time <200ms
- [ ] JWT verification time <50ms
- [ ] Database query optimization
- [ ] Memory usage is reasonable

## 🎯 Definition of Done
- [ ] Validation script runs successfully
- [ ] All authentication components are integrated
- [ ] Performance benchmarks are met
- [ ] Security validation passes
- [ ] End-to-end authentication workflow works
- [ ] Database schema is properly created
- [ ] All tests pass with >95% coverage
- [ ] Documentation is complete
- [ ] Code review is completed
