# Task 5.2: Implement User Management Interface

## 📋 Task Overview
**User Story:** HU-2.5 - Admin Interface  
**Task ID:** 5.2  
**Estimated Time:** 4 hours  
**Priority:** High  
**Complexity:** High  

## 🎯 Description
Create a comprehensive user management interface for tenant administrators that provides user listing with search and filters, user creation and editing modals, bulk operations interface, user activity tracking, and role assignment functionality.

## 📦 Deliverables
- [ ] Create `frontend/src/pages/admin/UserManagement.tsx`
- [ ] User list with search and filtering
- [ ] Create/edit user modals
- [ ] Bulk operations interface
- [ ] User activity tracking view

## ✅ Acceptance Criteria
- [ ] User list displays with pagination and search
- [ ] Search works across name, email, username fields
- [ ] Filter by role, status, and date ranges
- [ ] Create user modal with form validation
- [ ] Edit user modal with role assignment
- [ ] Bulk operations (activate, deactivate, delete)
- [ ] User activity history view
- [ ] Export functionality for user data
- [ ] Responsive design for mobile and desktop

## 🔧 Technical Requirements

### Component Structure
1. **UserManagement Main Component**
   - User list table with sorting
   - Search and filter controls
   - Action buttons and bulk operations
   - Pagination controls

2. **User Modals**
   - CreateUserModal with form validation
   - EditUserModal with role management
   - UserActivityModal for activity history
   - ConfirmationModal for destructive actions

3. **User Table Components**
   - UserRow with action buttons
   - UserStatusBadge for visual status
   - RoleBadge for role display
   - BulkActionToolbar

### State Management
- User list state with pagination
- Search and filter state
- Modal state management
- Loading and error states

## 🔗 Dependencies
- **Prerequisite:** Admin layout (Task 5.1), User management APIs
- **UI Components:** React, TypeScript, Tailwind CSS
- **State Management:** React hooks or Redux
- **Forms:** React Hook Form or Formik

## 📝 Code Example
```typescript
import React, { useState, useEffect, useCallback } from 'react';
import { useApi } from '../../hooks/useApi';
import { useAuth } from '../../hooks/useAuth';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { UserTable } from '../../components/admin/UserTable';
import { CreateUserModal } from '../../components/admin/CreateUserModal';
import { EditUserModal } from '../../components/admin/EditUserModal';
import { UserActivityModal } from '../../components/admin/UserActivityModal';
import { BulkActionToolbar } from '../../components/admin/BulkActionToolbar';
import { SearchAndFilters } from '../../components/admin/SearchAndFilters';
import { Pagination } from '../../components/common/Pagination';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Select } from '../../components/ui/Select';

interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  username?: string;
  role: 'owner' | 'admin' | 'user';
  is_active: boolean;
  created_at: string;
  last_login?: string;
  login_count: number;
}

interface UserFilters {
  search: string;
  role: string;
  status: string;
  created_after?: string;
  created_before?: string;
}

export const UserManagement: React.FC = () => {
  const { user } = useAuth();
  const { get, post, put, delete: deleteRequest } = useApi();
  
  // State management
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(25);
  
  // Filter and search state
  const [filters, setFilters] = useState<UserFilters>({
    search: '',
    role: '',
    status: ''
  });
  
  // Modal state
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showActivityModal, setShowActivityModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  
  // Bulk operations state
  const [selectedUsers, setSelectedUsers] = useState<Set<string>>(new Set());
  const [bulkLoading, setBulkLoading] = useState(false);

  // Load users with current filters and pagination
  const loadUsers = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        skip: ((currentPage - 1) * pageSize).toString(),
        limit: pageSize.toString(),
        ...(filters.search && { search_term: filters.search }),
        ...(filters.role && { role: filters.role }),
        ...(filters.status && { is_active: filters.status === 'active' ? 'true' : 'false' }),
        ...(filters.created_after && { created_after: filters.created_after }),
        ...(filters.created_before && { created_before: filters.created_before })
      });
      
      const response = await get(`/users?${params}`);
      setUsers(response.users);
      setTotalCount(response.total_count);
    } catch (err) {
      setError('Failed to load users');
      console.error('Error loading users:', err);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, filters, get]);

  useEffect(() => {
    loadUsers();
  }, [loadUsers]);

  // Handle search and filter changes
  const handleFilterChange = (newFilters: Partial<UserFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle user creation
  const handleCreateUser = async (userData: any) => {
    try {
      await post('/users', userData);
      setShowCreateModal(false);
      loadUsers();
      // Show success notification
    } catch (err) {
      console.error('Error creating user:', err);
      // Show error notification
    }
  };

  // Handle user editing
  const handleEditUser = async (userId: string, userData: any) => {
    try {
      await put(`/users/${userId}`, userData);
      setShowEditModal(false);
      setSelectedUser(null);
      loadUsers();
      // Show success notification
    } catch (err) {
      console.error('Error updating user:', err);
      // Show error notification
    }
  };

  // Handle user deletion
  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user?')) return;
    
    try {
      await deleteRequest(`/users/${userId}`);
      loadUsers();
      // Show success notification
    } catch (err) {
      console.error('Error deleting user:', err);
      // Show error notification
    }
  };

  // Handle bulk operations
  const handleBulkOperation = async (operation: string) => {
    if (selectedUsers.size === 0) return;
    
    setBulkLoading(true);
    try {
      const userIds = Array.from(selectedUsers);
      
      switch (operation) {
        case 'activate':
          await post('/users/bulk-activate', { user_ids: userIds });
          break;
        case 'deactivate':
          await post('/users/bulk-deactivate', { user_ids: userIds });
          break;
        case 'delete':
          if (!confirm(`Are you sure you want to delete ${userIds.length} users?`)) return;
          await post('/users/bulk-delete', { user_ids: userIds });
          break;
      }
      
      setSelectedUsers(new Set());
      loadUsers();
      // Show success notification
    } catch (err) {
      console.error('Error in bulk operation:', err);
      // Show error notification
    } finally {
      setBulkLoading(false);
    }
  };

  // Handle user selection for bulk operations
  const handleUserSelection = (userId: string, selected: boolean) => {
    const newSelection = new Set(selectedUsers);
    if (selected) {
      newSelection.add(userId);
    } else {
      newSelection.delete(userId);
    }
    setSelectedUsers(newSelection);
  };

  // Handle select all
  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedUsers(new Set(users.map(user => user.id)));
    } else {
      setSelectedUsers(new Set());
    }
  };

  // Export users
  const handleExport = async () => {
    try {
      const params = new URLSearchParams({
        ...(filters.search && { search_term: filters.search }),
        ...(filters.role && { role: filters.role }),
        ...(filters.status && { is_active: filters.status === 'active' ? 'true' : 'false' })
      });
      
      const response = await get(`/users/export?${params}`);
      
      // Create and download CSV file
      const blob = new Blob([response], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `users-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (err) {
      console.error('Error exporting users:', err);
      // Show error notification
    }
  };

  const totalPages = Math.ceil(totalCount / pageSize);

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
            <p className="text-gray-600">Manage users in your organization</p>
          </div>
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleExport}
              disabled={loading}
            >
              Export Users
            </Button>
            <Button
              onClick={() => setShowCreateModal(true)}
              disabled={loading}
            >
              Add User
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <SearchAndFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          loading={loading}
        />

        {/* Bulk Actions Toolbar */}
        {selectedUsers.size > 0 && (
          <BulkActionToolbar
            selectedCount={selectedUsers.size}
            onBulkOperation={handleBulkOperation}
            loading={bulkLoading}
          />
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading users</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
                <button
                  onClick={loadUsers}
                  className="mt-2 text-sm text-red-800 underline hover:text-red-900"
                >
                  Try again
                </button>
              </div>
            </div>
          </div>
        )}

        {/* User Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <UserTable
            users={users}
            loading={loading}
            selectedUsers={selectedUsers}
            onUserSelection={handleUserSelection}
            onSelectAll={handleSelectAll}
            onEditUser={(user) => {
              setSelectedUser(user);
              setShowEditModal(true);
            }}
            onDeleteUser={handleDeleteUser}
            onViewActivity={(user) => {
              setSelectedUser(user);
              setShowActivityModal(true);
            }}
          />
        </div>

        {/* Pagination */}
        {totalCount > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalCount={totalCount}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
            onPageSizeChange={(size) => {
              setPageSize(size);
              setCurrentPage(1);
            }}
          />
        )}

        {/* Modals */}
        {showCreateModal && (
          <CreateUserModal
            onClose={() => setShowCreateModal(false)}
            onSubmit={handleCreateUser}
          />
        )}

        {showEditModal && selectedUser && (
          <EditUserModal
            user={selectedUser}
            onClose={() => {
              setShowEditModal(false);
              setSelectedUser(null);
            }}
            onSubmit={(userData) => handleEditUser(selectedUser.id, userData)}
          />
        )}

        {showActivityModal && selectedUser && (
          <UserActivityModal
            user={selectedUser}
            onClose={() => {
              setShowActivityModal(false);
              setSelectedUser(null);
            }}
          />
        )}
      </div>
    </AdminLayout>
  );
};

// Supporting components would be implemented separately:
// - UserTable component with sorting and selection
// - SearchAndFilters component with form controls
// - BulkActionToolbar component with action buttons
// - CreateUserModal with form validation
// - EditUserModal with role management
// - UserActivityModal with activity history
// - Pagination component with page controls
```

## 🎯 Definition of Done
- [ ] User management interface is implemented
- [ ] User list with search and filtering works
- [ ] Create/edit user modals are functional
- [ ] Bulk operations work correctly
- [ ] User activity tracking is displayed
- [ ] Export functionality works
- [ ] Responsive design works on all devices
- [ ] Error handling is comprehensive
- [ ] Loading states are implemented
- [ ] Code review is completed
