# Task 3.2: Implement User Service

## 📋 Task Overview
**User Story:** HU-2.3 - User Management System  
**Task ID:** 3.2  
**Estimated Time:** 5 hours  
**Priority:** Critical  
**Complexity:** High  

## 🎯 Description
Create comprehensive business logic for user management that handles all user operations including CRUD, search, bulk operations, and activity tracking within the multi-tenant context.

## 📦 Deliverables
- [ ] Create `backend/src/services/user_service.py`
- [ ] Complete user CRUD operations
- [ ] User search and filtering functionality
- [ ] Bulk user operations
- [ ] User activity tracking
- [ ] User profile management

## ✅ Acceptance Criteria
- [ ] create_user with email uniqueness validation within tenant
- [ ] update_user with partial update support
- [ ] delete_user with soft deletion
- [ ] search_users with filtering by role, status, activity
- [ ] bulk_create_users for CSV import functionality
- [ ] bulk_update_users for mass operations
- [ ] get_user_activity for tracking user actions
- [ ] Proper error handling and validation for all operations

## 🔧 Technical Requirements

### Core Service Methods
1. **User CRUD Operations**
   - create_user, get_user, update_user, delete_user
   - Tenant isolation enforcement
   - Email uniqueness within tenant

2. **Search and Filtering**
   - search_users with multiple criteria
   - Pagination and sorting support
   - Performance optimization

3. **Bulk Operations**
   - bulk_create_users with error handling
   - bulk_update_users with validation
   - Progress tracking for large operations

4. **Activity Tracking**
   - User action logging
   - Activity retrieval and filtering
   - Performance optimization for logs

### Tenant Isolation
- All operations must respect tenant boundaries
- Use tenant context for RLS
- Prevent cross-tenant data access

## 🔗 Dependencies
- **Prerequisite:** User model (Task 1.1), Authentication (Tasks 1.1-1.5), Tenant management (US2)
- **Database:** User table with proper indexes and RLS policies
- **Models:** User and Tenant models must be properly related
- **Context:** Tenant context system for isolation

## 🧪 Testing Requirements
- [ ] Test user CRUD operations within tenant context
- [ ] Test email uniqueness enforcement within tenant
- [ ] Test search and filtering functionality
- [ ] Test bulk operations with success/failure scenarios
- [ ] Test user activity tracking and retrieval
- [ ] Test tenant isolation in all operations
- [ ] Test error handling and edge cases
- [ ] Performance tests for large user datasets

## 📊 Validation Checklist
- [ ] All operations respect tenant isolation
- [ ] Email uniqueness is enforced within tenant
- [ ] Search performance is acceptable (<300ms)
- [ ] Bulk operations handle errors gracefully
- [ ] Activity tracking is comprehensive
- [ ] Error handling provides useful feedback
- [ ] Type hints and documentation are complete

## 🚨 Security Considerations
- [ ] Enforce tenant isolation in all operations
- [ ] Validate user permissions for operations
- [ ] Prevent information disclosure in errors
- [ ] Log security-relevant activities
- [ ] Validate all input parameters
- [ ] Protect against injection attacks

## 📈 Performance Considerations
- [ ] Optimize database queries with proper indexes
- [ ] Implement pagination for large datasets
- [ ] Use bulk operations for efficiency
- [ ] Cache frequently accessed data
- [ ] Monitor query performance
- [ ] Avoid N+1 query problems

## 🔄 Implementation Steps
1. **Create UserService class structure**
2. **Implement basic CRUD operations**
3. **Add search and filtering functionality**
4. **Implement bulk operations**
5. **Add activity tracking**
6. **Implement comprehensive error handling**
7. **Add performance optimizations**
8. **Write comprehensive tests**

## 📝 Code Example
```python
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from fastapi import HTTPException, status
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from ..models.user import User
from ..models.tenant import Tenant
from ..schemas.user import UserCreate, UserUpdate, UserSearch
from ..core.security import get_password_hash
from ..core.tenant_context import tenant_context

class UserService:
    def __init__(self, db: Session):
        self.db = db

    def create_user(self, user_data: UserCreate, tenant_id: str) -> User:
        """Create a new user within tenant"""
        with tenant_context(self.db, tenant_id):
            # Check email uniqueness within tenant
            existing_user = self.db.query(User).filter(
                User.email == user_data.email,
                User.is_deleted == False
            ).first()

            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already exists in this tenant"
                )

            # Create new user
            hashed_password = get_password_hash(user_data.password)
            db_user = User(
                email=user_data.email,
                username=user_data.username,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                hashed_password=hashed_password,
                role=user_data.role,
                tenant_id=tenant_id,
                is_active=user_data.is_active
            )

            self.db.add(db_user)
            self.db.commit()
            self.db.refresh(db_user)

            # Log user creation activity
            self._log_user_activity(db_user.id, "user_created", {"created_by": "admin"})

            return db_user

    def get_user(self, user_id: str, tenant_id: str) -> Optional[User]:
        """Get user by ID within tenant"""
        with tenant_context(self.db, tenant_id):
            return self.db.query(User).filter(
                User.id == user_id,
                User.is_deleted == False
            ).first()

    def update_user(self, user_id: str, user_data: UserUpdate, tenant_id: str) -> User:
        """Update user within tenant"""
        with tenant_context(self.db, tenant_id):
            user = self.get_user(user_id, tenant_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            # Update fields
            update_data = user_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(user, field, value)

            self.db.commit()
            self.db.refresh(user)

            # Log user update activity
            self._log_user_activity(user.id, "user_updated", {"fields": list(update_data.keys())})

            return user

    def delete_user(self, user_id: str, tenant_id: str) -> bool:
        """Soft delete user within tenant"""
        with tenant_context(self.db, tenant_id):
            user = self.get_user(user_id, tenant_id)
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="User not found"
                )

            user.soft_delete()
            self.db.commit()

            # Log user deletion activity
            self._log_user_activity(user.id, "user_deleted", {"deleted_by": "admin"})

            return True

    def search_users(
        self,
        tenant_id: str,
        search_params: UserSearch,
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """Search users with filtering and pagination"""
        with tenant_context(self.db, tenant_id):
            query = self.db.query(User).filter(User.is_deleted == False)

            # Apply filters
            if search_params.search_term:
                search_term = f"%{search_params.search_term}%"
                query = query.filter(
                    or_(
                        User.first_name.ilike(search_term),
                        User.last_name.ilike(search_term),
                        User.email.ilike(search_term),
                        User.username.ilike(search_term)
                    )
                )

            if search_params.role:
                query = query.filter(User.role == search_params.role)

            if search_params.is_active is not None:
                query = query.filter(User.is_active == search_params.is_active)

            if search_params.created_after:
                query = query.filter(User.created_at >= search_params.created_after)

            if search_params.last_login_after:
                query = query.filter(User.last_login >= search_params.last_login_after)

            # Get total count
            total_count = query.count()

            # Apply sorting
            if search_params.sort_by:
                sort_field = getattr(User, search_params.sort_by, None)
                if sort_field:
                    if search_params.sort_order == "desc":
                        query = query.order_by(sort_field.desc())
                    else:
                        query = query.order_by(sort_field.asc())

            # Apply pagination
            users = query.offset(skip).limit(limit).all()

            return {
                "users": users,
                "total_count": total_count,
                "page": skip // limit + 1,
                "pages": (total_count + limit - 1) // limit
            }

    def bulk_create_users(
        self,
        users_data: List[UserCreate],
        tenant_id: str
    ) -> Dict[str, Any]:
        """Bulk create users with error handling"""
        results = {
            "successful": [],
            "failed": [],
            "total": len(users_data)
        }

        with tenant_context(self.db, tenant_id):
            for i, user_data in enumerate(users_data):
                try:
                    user = self.create_user(user_data, tenant_id)
                    results["successful"].append({
                        "index": i,
                        "user_id": user.id,
                        "email": user.email
                    })
                except Exception as e:
                    results["failed"].append({
                        "index": i,
                        "email": user_data.email,
                        "error": str(e)
                    })

        return results

    def get_user_activity(
        self,
        user_id: str,
        tenant_id: str,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Get user activity logs"""
        # Implementation depends on activity logging system
        # This is a placeholder for the activity tracking functionality
        pass

    def _log_user_activity(
        self,
        user_id: str,
        action: str,
        metadata: Dict[str, Any] = None
    ):
        """Log user activity for audit trail"""
        # Implementation for activity logging
        # This could be a separate ActivityLog model
        pass
```

## 🔍 Test Examples
```python
def test_create_user_success(test_db, sample_tenant):
    """Test successful user creation"""
    user_service = UserService(test_db)
    
    user_data = UserCreate(
        email="<EMAIL>",
        password="securepass123",
        first_name="John",
        last_name="Doe",
        role="user"
    )
    
    user = user_service.create_user(user_data, sample_tenant.id)
    
    assert user.email == "<EMAIL>"
    assert user.tenant_id == sample_tenant.id
    assert user.is_active == True

def test_email_uniqueness_within_tenant(test_db, sample_tenant):
    """Test email uniqueness enforcement within tenant"""
    user_service = UserService(test_db)
    
    user_data = UserCreate(
        email="<EMAIL>",
        password="securepass123"
    )
    
    # Create first user
    user_service.create_user(user_data, sample_tenant.id)
    
    # Try to create duplicate
    with pytest.raises(HTTPException) as exc_info:
        user_service.create_user(user_data, sample_tenant.id)
    
    assert exc_info.value.status_code == 400
    assert "already exists" in exc_info.value.detail

def test_search_users(test_db, sample_tenant, sample_users):
    """Test user search functionality"""
    user_service = UserService(test_db)
    
    search_params = UserSearch(
        search_term="john",
        role="user",
        is_active=True
    )
    
    results = user_service.search_users(
        sample_tenant.id,
        search_params,
        skip=0,
        limit=10
    )
    
    assert "users" in results
    assert "total_count" in results
    assert results["total_count"] >= 0
```

## 🎯 Definition of Done
- [ ] UserService class is fully implemented
- [ ] All CRUD operations work within tenant context
- [ ] Search and filtering functionality is complete
- [ ] Bulk operations handle success/failure scenarios
- [ ] User activity tracking is implemented
- [ ] Email uniqueness is enforced within tenant
- [ ] Error handling is comprehensive
- [ ] All tests pass with >95% coverage
- [ ] Performance requirements are met
- [ ] Code review is completed
