# Task 2.5: Implement Tenant Statistics

## 📋 Task Overview
**User Story:** HU-2.2 - Tenant CRUD Operations  
**Task ID:** 2.5  
**Estimated Time:** 1.5 hours  
**Priority:** Medium  
**Complexity:** Medium  

## 🎯 Description
Create analytics and usage statistics for tenants including user count aggregation, active user statistics, tenant usage metrics, performance optimization for statistics, and caching strategy for expensive queries.

## 📦 Deliverables
- [ ] User count aggregation queries
- [ ] Active user statistics
- [ ] Tenant usage metrics
- [ ] Performance optimization for statistics
- [ ] Caching strategy for expensive queries

## ✅ Acceptance Criteria
- [ ] get_tenants_with_stats efficiently calculates user counts
- [ ] Active user count based on recent login activity
- [ ] Statistics queries are optimized with proper indexes
- [ ] Caching implemented for frequently accessed statistics
- [ ] Statistics update in real-time or near real-time
- [ ] Performance testing shows acceptable response times
- [ ] Statistics are accurate and consistent

## 🔧 Technical Requirements

### Statistics Categories
1. **User Statistics**
   - Total user count per tenant
   - Active users (logged in last 30 days)
   - New users (registered last 30 days)
   - User growth trends

2. **Activity Statistics**
   - Login frequency
   - Feature usage
   - Content creation metrics
   - Engagement metrics

3. **System Statistics**
   - Storage usage
   - API usage
   - Performance metrics

### Performance Requirements
- Statistics queries <200ms
- Real-time updates for critical metrics
- Efficient aggregation queries
- Proper database indexing

## 🔗 Dependencies
- **Prerequisite:** Tenant and User models, database optimization
- **Caching:** Redis or in-memory caching
- **Analytics:** Database aggregation functions

## 📝 Code Example
```python
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, text
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
import redis
import json

from ..models.tenant import Tenant
from ..models.user import User
from ..core.database import get_db
from ..core.config import settings

@dataclass
class TenantStatistics:
    tenant_id: str
    tenant_name: str
    total_users: int
    active_users: int
    new_users_30d: int
    total_logins: int
    avg_session_duration: float
    storage_used: int
    last_activity: Optional[datetime]
    growth_rate: float

class TenantStatisticsService:
    def __init__(self, db: Session):
        self.db = db
        self.redis_client = redis.Redis.from_url(settings.REDIS_URL) if hasattr(settings, 'REDIS_URL') else None
        self.cache_ttl = 300  # 5 minutes

    def get_tenant_statistics(self, tenant_id: str) -> TenantStatistics:
        """Get comprehensive statistics for a specific tenant"""
        # Try cache first
        cache_key = f"tenant_stats:{tenant_id}"
        if self.redis_client:
            cached_stats = self.redis_client.get(cache_key)
            if cached_stats:
                data = json.loads(cached_stats)
                return TenantStatistics(**data)

        # Calculate statistics
        stats = self._calculate_tenant_statistics(tenant_id)
        
        # Cache results
        if self.redis_client:
            self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                json.dumps(stats.__dict__, default=str)
            )
        
        return stats

    def get_all_tenants_statistics(self) -> List[TenantStatistics]:
        """Get statistics for all tenants"""
        cache_key = "all_tenants_stats"
        if self.redis_client:
            cached_stats = self.redis_client.get(cache_key)
            if cached_stats:
                data = json.loads(cached_stats)
                return [TenantStatistics(**item) for item in data]

        # Calculate statistics for all tenants
        tenants = self.db.query(Tenant).filter(Tenant.is_deleted == False).all()
        all_stats = []
        
        for tenant in tenants:
            stats = self._calculate_tenant_statistics(tenant.id)
            all_stats.append(stats)
        
        # Cache results
        if self.redis_client:
            stats_data = [stats.__dict__ for stats in all_stats]
            self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                json.dumps(stats_data, default=str)
            )
        
        return all_stats

    def _calculate_tenant_statistics(self, tenant_id: str) -> TenantStatistics:
        """Calculate statistics for a specific tenant"""
        # Get tenant info
        tenant = self.db.query(Tenant).filter(Tenant.id == tenant_id).first()
        if not tenant:
            raise ValueError(f"Tenant {tenant_id} not found")

        # Date ranges
        now = datetime.utcnow()
        thirty_days_ago = now - timedelta(days=30)
        sixty_days_ago = now - timedelta(days=60)

        # Base user query for this tenant
        user_query = self.db.query(User).filter(
            User.tenant_id == tenant_id,
            User.is_deleted == False
        )

        # Total users
        total_users = user_query.count()

        # Active users (logged in last 30 days)
        active_users = user_query.filter(
            User.last_login >= thirty_days_ago
        ).count()

        # New users (registered last 30 days)
        new_users_30d = user_query.filter(
            User.created_at >= thirty_days_ago
        ).count()

        # Total logins (sum of login_count)
        total_logins_result = user_query.with_entities(
            func.sum(User.login_count)
        ).scalar()
        total_logins = total_logins_result or 0

        # Last activity (most recent login)
        last_activity_result = user_query.with_entities(
            func.max(User.last_login)
        ).scalar()

        # Growth rate calculation
        users_60d_ago = user_query.filter(
            User.created_at <= sixty_days_ago
        ).count()
        users_30d_ago = user_query.filter(
            User.created_at <= thirty_days_ago
        ).count()
        
        if users_30d_ago > 0:
            growth_rate = ((total_users - users_30d_ago) / users_30d_ago) * 100
        else:
            growth_rate = 0.0

        # Average session duration (placeholder - would need session tracking)
        avg_session_duration = 0.0

        # Storage used (placeholder - would need file tracking)
        storage_used = 0

        return TenantStatistics(
            tenant_id=tenant_id,
            tenant_name=tenant.name,
            total_users=total_users,
            active_users=active_users,
            new_users_30d=new_users_30d,
            total_logins=total_logins,
            avg_session_duration=avg_session_duration,
            storage_used=storage_used,
            last_activity=last_activity_result,
            growth_rate=growth_rate
        )

    def get_tenant_user_growth(self, tenant_id: str, days: int = 30) -> Dict[str, Any]:
        """Get user growth data for charts"""
        cache_key = f"tenant_growth:{tenant_id}:{days}"
        if self.redis_client:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)

        # Calculate daily user registrations
        end_date = datetime.utcnow().date()
        start_date = end_date - timedelta(days=days)

        # Query for daily registrations
        daily_registrations = self.db.query(
            func.date(User.created_at).label('date'),
            func.count(User.id).label('count')
        ).filter(
            User.tenant_id == tenant_id,
            User.is_deleted == False,
            func.date(User.created_at) >= start_date
        ).group_by(
            func.date(User.created_at)
        ).order_by(
            func.date(User.created_at)
        ).all()

        # Format data for charts
        growth_data = {
            'labels': [],
            'registrations': [],
            'cumulative': []
        }

        cumulative_count = 0
        for reg in daily_registrations:
            growth_data['labels'].append(reg.date.strftime('%Y-%m-%d'))
            growth_data['registrations'].append(reg.count)
            cumulative_count += reg.count
            growth_data['cumulative'].append(cumulative_count)

        # Cache results
        if self.redis_client:
            self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                json.dumps(growth_data, default=str)
            )

        return growth_data

    def get_tenant_activity_metrics(self, tenant_id: str) -> Dict[str, Any]:
        """Get activity metrics for a tenant"""
        cache_key = f"tenant_activity:{tenant_id}"
        if self.redis_client:
            cached_data = self.redis_client.get(cache_key)
            if cached_data:
                return json.loads(cached_data)

        # Calculate activity metrics
        now = datetime.utcnow()
        last_7_days = now - timedelta(days=7)
        last_30_days = now - timedelta(days=30)

        user_query = self.db.query(User).filter(
            User.tenant_id == tenant_id,
            User.is_deleted == False
        )

        # Daily active users (last 7 days)
        dau = user_query.filter(
            User.last_login >= last_7_days
        ).count()

        # Monthly active users (last 30 days)
        mau = user_query.filter(
            User.last_login >= last_30_days
        ).count()

        # User engagement ratio
        total_users = user_query.count()
        engagement_ratio = (mau / total_users * 100) if total_users > 0 else 0

        # Login frequency distribution
        login_frequency = self.db.query(
            func.case([
                (User.login_count == 0, 'Never'),
                (User.login_count.between(1, 5), '1-5 times'),
                (User.login_count.between(6, 20), '6-20 times'),
                (User.login_count.between(21, 50), '21-50 times'),
                (User.login_count > 50, '50+ times')
            ]).label('frequency'),
            func.count(User.id).label('count')
        ).filter(
            User.tenant_id == tenant_id,
            User.is_deleted == False
        ).group_by('frequency').all()

        activity_data = {
            'daily_active_users': dau,
            'monthly_active_users': mau,
            'engagement_ratio': round(engagement_ratio, 2),
            'login_frequency': {freq.frequency: freq.count for freq in login_frequency}
        }

        # Cache results
        if self.redis_client:
            self.redis_client.setex(
                cache_key,
                self.cache_ttl,
                json.dumps(activity_data)
            )

        return activity_data

    def invalidate_tenant_cache(self, tenant_id: str):
        """Invalidate cached statistics for a tenant"""
        if self.redis_client:
            cache_keys = [
                f"tenant_stats:{tenant_id}",
                f"tenant_growth:{tenant_id}:*",
                f"tenant_activity:{tenant_id}",
                "all_tenants_stats"
            ]
            
            for key in cache_keys:
                if '*' in key:
                    # Delete pattern
                    for k in self.redis_client.scan_iter(match=key):
                        self.redis_client.delete(k)
                else:
                    self.redis_client.delete(key)

    def refresh_all_statistics(self):
        """Refresh all cached statistics"""
        if self.redis_client:
            # Clear all cached statistics
            for key in self.redis_client.scan_iter(match="tenant_*"):
                self.redis_client.delete(key)
            
            # Pre-calculate statistics for all tenants
            self.get_all_tenants_statistics()

# Database optimization queries
def create_statistics_indexes(db: Session):
    """Create indexes for optimal statistics performance"""
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_users_tenant_created ON users(tenant_id, created_at)",
        "CREATE INDEX IF NOT EXISTS idx_users_tenant_last_login ON users(tenant_id, last_login)",
        "CREATE INDEX IF NOT EXISTS idx_users_tenant_active ON users(tenant_id, is_deleted, is_active)",
        "CREATE INDEX IF NOT EXISTS idx_users_login_count ON users(login_count)",
    ]
    
    for index_sql in indexes:
        try:
            db.execute(text(index_sql))
            db.commit()
        except Exception as e:
            print(f"Index creation failed: {e}")
            db.rollback()

# API endpoint for statistics
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

router = APIRouter()

@router.get("/stats")
async def get_tenant_statistics(
    tenant_id: str,
    db: Session = Depends(get_db)
):
    """Get comprehensive tenant statistics"""
    stats_service = TenantStatisticsService(db)
    return stats_service.get_tenant_statistics(tenant_id)

@router.get("/stats/growth")
async def get_tenant_growth(
    tenant_id: str,
    days: int = 30,
    db: Session = Depends(get_db)
):
    """Get tenant user growth data"""
    stats_service = TenantStatisticsService(db)
    return stats_service.get_tenant_user_growth(tenant_id, days)

@router.get("/stats/activity")
async def get_tenant_activity(
    tenant_id: str,
    db: Session = Depends(get_db)
):
    """Get tenant activity metrics"""
    stats_service = TenantStatisticsService(db)
    return stats_service.get_tenant_activity_metrics(tenant_id)
```

## 🎯 Definition of Done
- [ ] Tenant statistics service is implemented
- [ ] User count aggregation is optimized
- [ ] Active user calculations work correctly
- [ ] Caching strategy improves performance
- [ ] Statistics queries meet performance targets
- [ ] Database indexes are optimized
- [ ] API endpoints return accurate data
- [ ] Code review is completed
