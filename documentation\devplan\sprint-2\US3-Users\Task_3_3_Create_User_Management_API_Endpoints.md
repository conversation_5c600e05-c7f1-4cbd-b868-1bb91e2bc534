# Task 3.3: Create User Management API Endpoints

## 📋 Task Overview
**User Story:** HU-2.3 - User Management System  
**Task ID:** 3.3  
**Estimated Time:** 4 hours  
**Priority:** Critical  
**Complexity:** High  

## 🎯 Description
Implement comprehensive API endpoints for user management that provide CRUD operations, search and filtering, bulk operations, and user activity tracking with proper authorization and tenant isolation.

## 📦 Deliverables
- [ ] Create `backend/src/api/v1/endpoints/users.py`
- [ ] User CRUD endpoints with proper authorization
- [ ] User search and filtering endpoints
- [ ] Bulk operation endpoints
- [ ] User activity and profile endpoints

## ✅ Acceptance Criteria
- [ ] GET /users with pagination, search, and filtering
- [ ] POST /users for creating individual users
- [ ] GET /users/{user_id} for user details
- [ ] PUT /users/{user_id} for user updates
- [ ] DELETE /users/{user_id} for user deletion
- [ ] POST /users/bulk for bulk user creation
- [ ] PUT /users/bulk for bulk user updates
- [ ] GET /users/{user_id}/activity for user activity logs
- [ ] All endpoints enforce tenant isolation and role permissions

## 🔧 Technical Requirements

### Endpoint Categories
1. **Individual User Operations**
   - GET, POST, PUT, DELETE for single users
2. **Bulk Operations**
   - Bulk create, update, delete
3. **Search and Analytics**
   - Advanced search, filtering, statistics
4. **Activity Tracking**
   - User activity logs and audit trails

### Security Requirements
- Tenant isolation enforcement
- Role-based access control
- Input validation and sanitization
- Rate limiting for bulk operations

## 📝 Code Example
```python
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional

from ....core.database import get_db
from ....core.deps import get_current_admin_user, get_current_tenant, get_current_active_user
from ....models.user import User
from ....models.tenant import Tenant
from ....schemas.user import (
    UserInDB, UserCreate, UserUpdate, UserSearch, UserListResponse,
    BulkUserCreate, BulkUserUpdate, BulkOperationResult, UserActivity, UserStats
)
from ....services.user_service import UserService
from ....core.tenant_context import tenant_context

router = APIRouter()

@router.get("/", response_model=UserListResponse)
async def get_users(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    search_term: Optional[str] = Query(None),
    role: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    sort_by: Optional[str] = Query("created_at"),
    sort_order: Optional[str] = Query("desc"),
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get all users in the current tenant with search and filtering"""
    user_service = UserService(db)
    
    search_params = UserSearch(
        search_term=search_term,
        role=role,
        is_active=is_active,
        sort_by=sort_by,
        sort_order=sort_order
    )
    
    result = user_service.search_users(
        current_tenant.id,
        search_params,
        skip=skip,
        limit=limit
    )
    
    return UserListResponse(
        users=result["users"],
        total_count=result["total_count"],
        page=result["page"],
        pages=result["pages"],
        has_next=result["page"] < result["pages"],
        has_prev=result["page"] > 1
    )

@router.post("/", response_model=UserInDB)
async def create_user(
    user_data: UserCreate,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Create a new user in the current tenant"""
    user_service = UserService(db)
    return user_service.create_user(user_data, current_tenant.id)

@router.get("/{user_id}", response_model=UserInDB)
async def get_user(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get a specific user by ID"""
    user_service = UserService(db)
    user = user_service.get_user(user_id, current_tenant.id)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user

@router.put("/{user_id}", response_model=UserInDB)
async def update_user(
    user_id: str,
    user_data: UserUpdate,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Update a user"""
    # Check if trying to update own role (prevent self-privilege escalation)
    if user_id == current_user.id and user_data.role is not None:
        if user_data.role != current_user.role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot change your own role"
            )
    
    user_service = UserService(db)
    return user_service.update_user(user_id, user_data, current_tenant.id)

@router.delete("/{user_id}")
async def delete_user(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Soft delete a user"""
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete yourself"
        )
    
    user_service = UserService(db)
    success = user_service.delete_user(user_id, current_tenant.id)
    
    if success:
        return {"message": "User deleted successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete user"
        )

@router.post("/bulk", response_model=BulkOperationResult)
async def bulk_create_users(
    bulk_data: BulkUserCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Bulk create users"""
    user_service = UserService(db)
    
    # For large operations, process in background
    if len(bulk_data.users) > 100:
        # Start background task
        background_tasks.add_task(
            user_service.bulk_create_users_async,
            bulk_data.users,
            current_tenant.id,
            current_user.id
        )
        
        return BulkOperationResult(
            total=len(bulk_data.users),
            successful=0,
            failed=0,
            errors=[{"message": "Bulk operation started in background"}]
        )
    else:
        # Process immediately for small operations
        return user_service.bulk_create_users(bulk_data.users, current_tenant.id)

@router.put("/bulk", response_model=BulkOperationResult)
async def bulk_update_users(
    bulk_data: BulkUserUpdate,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Bulk update users"""
    # Prevent updating own user in bulk operations
    if current_user.id in bulk_data.user_ids:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot include yourself in bulk update operations"
        )
    
    user_service = UserService(db)
    return user_service.bulk_update_users(
        bulk_data.user_ids,
        bulk_data.updates,
        current_tenant.id
    )

@router.get("/{user_id}/activity", response_model=List[UserActivity])
async def get_user_activity(
    user_id: str,
    limit: int = Query(50, ge=1, le=1000),
    skip: int = Query(0, ge=0),
    action_filter: Optional[str] = Query(None),
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get user activity logs"""
    # Users can view their own activity, admins can view any user's activity
    if user_id != current_user.id and not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions to view user activity"
        )
    
    user_service = UserService(db)
    return user_service.get_user_activity(
        user_id,
        current_tenant.id,
        limit=limit,
        skip=skip,
        action_filter=action_filter
    )

@router.get("/stats/overview", response_model=UserStats)
async def get_user_statistics(
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get user statistics for the tenant"""
    user_service = UserService(db)
    return user_service.get_user_statistics(current_tenant.id)

@router.get("/me", response_model=UserInDB)
async def get_current_user_profile(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user's profile"""
    return current_user

@router.put("/me", response_model=UserInDB)
async def update_current_user_profile(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update current user's profile"""
    # Users cannot change their own role or active status
    if user_data.role is not None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot change your own role"
        )
    
    if user_data.is_active is not None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot change your own active status"
        )
    
    user_service = UserService(db)
    return user_service.update_user(current_user.id, user_data, current_user.tenant_id)

@router.post("/{user_id}/activate")
async def activate_user(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Activate a user"""
    user_service = UserService(db)
    update_data = UserUpdate(is_active=True)
    updated_user = user_service.update_user(user_id, update_data, current_tenant.id)
    
    return {"message": f"User {updated_user.email} activated successfully"}

@router.post("/{user_id}/deactivate")
async def deactivate_user(
    user_id: str,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Deactivate a user"""
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot deactivate yourself"
        )
    
    user_service = UserService(db)
    update_data = UserUpdate(is_active=False)
    updated_user = user_service.update_user(user_id, update_data, current_tenant.id)
    
    return {"message": f"User {updated_user.email} deactivated successfully"}
```

## 🎯 Definition of Done
- [ ] All user management endpoints are implemented
- [ ] CRUD operations work with proper authorization
- [ ] Search and filtering functionality is complete
- [ ] Bulk operations handle large datasets
- [ ] Activity tracking endpoints are functional
- [ ] Tenant isolation is enforced
- [ ] Error handling is comprehensive
- [ ] All tests pass
- [ ] API documentation is complete
- [ ] Code review is completed
