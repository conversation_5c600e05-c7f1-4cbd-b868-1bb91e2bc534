# Task 4.5: Create Permission-Protected Endpoints

## 📋 Task Overview
**User Story:** HU-2.4 - Role-Based Access Control  
**Task ID:** 4.5  
**Estimated Time:** 3 hours  
**Priority:** Critical  
**Complexity:** High  

## 🎯 Description
Apply permission-based protection to all API endpoints using the authorization middleware, implement role-based access control for different endpoint categories, create permission matrices for endpoint access, and ensure comprehensive security coverage.

## 📦 Deliverables
- [ ] Apply permission decorators to all API endpoints
- [ ] Role-based access control for endpoint categories
- [ ] Permission matrices for endpoint access
- [ ] Comprehensive security coverage testing

## ✅ Acceptance Criteria
- [ ] All endpoints have appropriate permission requirements
- [ ] System owner endpoints require owner role
- [ ] Tenant admin endpoints require admin role or higher
- [ ] User endpoints have proper role-based access
- [ ] Permission inheritance works correctly
- [ ] Unauthorized access returns proper 403 responses
- [ ] Permission audit logging captures access attempts
- [ ] Performance impact is minimal (<10ms overhead)

## 🔧 Technical Requirements

### Endpoint Categories
1. **System Owner Endpoints**
   - Tenant management (create, delete, view all)
   - System settings and configuration
   - Global analytics and reporting

2. **Tenant Admin Endpoints**
   - User management within tenant
   - Tenant settings and customization
   - Tenant analytics and reports

3. **User Endpoints**
   - Profile management
   - Content access
   - Basic platform features

### Permission Strategy
- Use decorators for endpoint protection
- Implement permission inheritance
- Context-aware permission checking
- Audit logging for security events

## 🔗 Dependencies
- **Prerequisite:** Authorization middleware (Task 4.2), Permission system (Task 4.1)
- **Endpoints:** All existing API endpoints
- **Security:** Comprehensive permission definitions

## 📝 Code Example
```python
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List

from ....core.database import get_db
from ....core.deps import get_current_user, get_current_active_user, get_current_admin_user
from ....middleware.auth_middleware import require_permission, require_role, require_owner
from ....core.permissions import Permission, Role
from ....models.user import User
from ....models.tenant import Tenant
from ....schemas.user import UserInDB, UserCreate, UserUpdate
from ....schemas.tenant import TenantInDB, TenantCreate, TenantUpdate
from ....services.user_service import UserService
from ....services.tenant_service import TenantService

# ======================
# System Owner Endpoints
# ======================

system_router = APIRouter(prefix="/system", tags=["system"])

@system_router.get("/tenants", response_model=List[TenantInDB])
@require_permission(Permission.SYSTEM_VIEW_ALL_TENANTS)
async def get_all_tenants(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all tenants (system owner only)"""
    tenant_service = TenantService(db)
    return tenant_service.get_all_tenants()

@system_router.post("/tenants", response_model=TenantInDB)
@require_permission(Permission.SYSTEM_MANAGE_TENANTS)
async def create_tenant(
    tenant_data: TenantCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create new tenant (system owner only)"""
    tenant_service = TenantService(db)
    return tenant_service.create_tenant(tenant_data)

@system_router.delete("/tenants/{tenant_id}")
@require_permission(Permission.SYSTEM_MANAGE_TENANTS)
async def delete_tenant(
    tenant_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete tenant (system owner only)"""
    tenant_service = TenantService(db)
    return tenant_service.delete_tenant(tenant_id)

@system_router.get("/settings")
@require_permission(Permission.SYSTEM_MANAGE_SETTINGS)
async def get_system_settings(
    current_user: User = Depends(get_current_active_user)
):
    """Get system settings (system owner only)"""
    # Implementation for system settings
    return {"settings": "system_configuration"}

@system_router.put("/settings")
@require_permission(Permission.SYSTEM_MANAGE_SETTINGS)
async def update_system_settings(
    settings_data: dict,
    current_user: User = Depends(get_current_active_user)
):
    """Update system settings (system owner only)"""
    # Implementation for updating system settings
    return {"message": "System settings updated"}

# ======================
# Tenant Admin Endpoints
# ======================

admin_router = APIRouter(prefix="/admin", tags=["admin"])

@admin_router.get("/users", response_model=List[UserInDB])
@require_permission(Permission.TENANT_MANAGE_USERS)
async def get_tenant_users(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get all users in current tenant (admin only)"""
    user_service = UserService(db)
    return user_service.get_tenant_users(current_user.tenant_id)

@admin_router.post("/users", response_model=UserInDB)
@require_permission(Permission.TENANT_MANAGE_USERS)
async def create_tenant_user(
    user_data: UserCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create user in current tenant (admin only)"""
    user_service = UserService(db)
    return user_service.create_user(user_data, current_user.tenant_id)

@admin_router.put("/users/{user_id}", response_model=UserInDB)
@require_permission(Permission.TENANT_MANAGE_USERS)
async def update_tenant_user(
    user_id: str,
    user_data: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update user in current tenant (admin only)"""
    user_service = UserService(db)
    return user_service.update_user(user_id, user_data, current_user.tenant_id)

@admin_router.delete("/users/{user_id}")
@require_permission(Permission.TENANT_MANAGE_USERS)
async def delete_tenant_user(
    user_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete user from current tenant (admin only)"""
    # Prevent self-deletion
    if user_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete yourself"
        )
    
    user_service = UserService(db)
    return user_service.delete_user(user_id, current_user.tenant_id)

@admin_router.get("/tenant/settings")
@require_permission(Permission.TENANT_MANAGE_SETTINGS)
async def get_tenant_settings(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get tenant settings (admin only)"""
    tenant_service = TenantService(db)
    tenant = tenant_service.get_tenant_by_id(current_user.tenant_id)
    return {"settings": tenant.settings}

@admin_router.put("/tenant/settings")
@require_permission(Permission.TENANT_MANAGE_SETTINGS)
async def update_tenant_settings(
    settings_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update tenant settings (admin only)"""
    tenant_service = TenantService(db)
    tenant_update = TenantUpdate(settings=settings_data)
    return tenant_service.update_tenant(current_user.tenant_id, tenant_update)

@admin_router.get("/analytics")
@require_permission(Permission.TENANT_VIEW_ANALYTICS)
async def get_tenant_analytics(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get tenant analytics (admin only)"""
    # Implementation for tenant analytics
    return {"analytics": "tenant_data"}

@admin_router.get("/invitations")
@require_permission(Permission.TENANT_MANAGE_INVITATIONS)
async def get_tenant_invitations(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get tenant invitations (admin only)"""
    # Implementation for invitation management
    return {"invitations": []}

@admin_router.post("/invitations")
@require_permission(Permission.TENANT_MANAGE_INVITATIONS)
async def create_invitation(
    invitation_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create invitation (admin only)"""
    # Implementation for creating invitations
    return {"message": "Invitation created"}

# ======================
# User Endpoints
# ======================

user_router = APIRouter(prefix="/user", tags=["user"])

@user_router.get("/profile", response_model=UserInDB)
@require_permission(Permission.USER_VIEW_PROFILE)
async def get_user_profile(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user profile"""
    return current_user

@user_router.put("/profile", response_model=UserInDB)
@require_permission(Permission.USER_UPDATE_PROFILE)
async def update_user_profile(
    profile_data: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update current user profile"""
    # Users cannot change their own role or active status
    if profile_data.role is not None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot change your own role"
        )
    
    if profile_data.is_active is not None:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot change your own active status"
        )
    
    user_service = UserService(db)
    return user_service.update_user(current_user.id, profile_data, current_user.tenant_id)

@user_router.get("/content")
@require_permission(Permission.USER_VIEW_CONTENT)
async def get_user_content(
    current_user: User = Depends(get_current_active_user)
):
    """Get content accessible to user"""
    # Implementation for user content access
    return {"content": "user_accessible_content"}

# ======================
# Course Management Endpoints
# ======================

course_router = APIRouter(prefix="/courses", tags=["courses"])

@course_router.get("/")
@require_permission(Permission.COURSE_VIEW)
async def get_courses(
    current_user: User = Depends(get_current_active_user)
):
    """Get courses accessible to user"""
    # Implementation for course listing
    return {"courses": []}

@course_router.post("/")
@require_permission(Permission.COURSE_CREATE)
async def create_course(
    course_data: dict,
    current_user: User = Depends(get_current_active_user)
):
    """Create new course (requires course creation permission)"""
    # Implementation for course creation
    return {"message": "Course created"}

@course_router.put("/{course_id}")
@require_permission(Permission.COURSE_EDIT)
async def update_course(
    course_id: str,
    course_data: dict,
    current_user: User = Depends(get_current_active_user)
):
    """Update course (requires course edit permission)"""
    # Implementation for course updates
    return {"message": "Course updated"}

@course_router.delete("/{course_id}")
@require_permission(Permission.COURSE_DELETE)
async def delete_course(
    course_id: str,
    current_user: User = Depends(get_current_active_user)
):
    """Delete course (requires course delete permission)"""
    # Implementation for course deletion
    return {"message": "Course deleted"}

# ======================
# Permission Testing Endpoints
# ======================

test_router = APIRouter(prefix="/test", tags=["permission-tests"])

@test_router.get("/owner-only")
@require_role(Role.OWNER)
async def owner_only_endpoint(
    current_user: User = Depends(get_current_active_user)
):
    """Test endpoint for owner role only"""
    return {"message": "You are a system owner", "user": current_user.email}

@test_router.get("/admin-only")
@require_role(Role.ADMIN)
async def admin_only_endpoint(
    current_user: User = Depends(get_current_active_user)
):
    """Test endpoint for admin role or higher"""
    return {"message": "You are an admin or owner", "user": current_user.email}

@test_router.get("/user-only")
@require_role(Role.USER)
async def user_only_endpoint(
    current_user: User = Depends(get_current_active_user)
):
    """Test endpoint for any authenticated user"""
    return {"message": "You are authenticated", "user": current_user.email}

@test_router.get("/multiple-permissions")
@require_permission(Permission.TENANT_MANAGE_USERS)
@require_permission(Permission.TENANT_VIEW_ANALYTICS)
async def multiple_permissions_endpoint(
    current_user: User = Depends(get_current_active_user)
):
    """Test endpoint requiring multiple permissions"""
    return {"message": "You have multiple permissions", "user": current_user.email}

# ======================
# Permission Matrix Documentation
# ======================

PERMISSION_MATRIX = {
    "System Owner": {
        "permissions": [
            Permission.SYSTEM_MANAGE_TENANTS,
            Permission.SYSTEM_VIEW_ALL_TENANTS,
            Permission.SYSTEM_MANAGE_SETTINGS,
            # Inherits all admin and user permissions
        ],
        "endpoints": [
            "GET /system/tenants",
            "POST /system/tenants",
            "DELETE /system/tenants/{id}",
            "GET /system/settings",
            "PUT /system/settings",
            # Plus all admin and user endpoints
        ]
    },
    "Tenant Admin": {
        "permissions": [
            Permission.TENANT_MANAGE_USERS,
            Permission.TENANT_MANAGE_SETTINGS,
            Permission.TENANT_VIEW_ANALYTICS,
            Permission.TENANT_MANAGE_INVITATIONS,
            # Inherits all user permissions
        ],
        "endpoints": [
            "GET /admin/users",
            "POST /admin/users",
            "PUT /admin/users/{id}",
            "DELETE /admin/users/{id}",
            "GET /admin/tenant/settings",
            "PUT /admin/tenant/settings",
            "GET /admin/analytics",
            "GET /admin/invitations",
            "POST /admin/invitations",
            # Plus all user endpoints
        ]
    },
    "User": {
        "permissions": [
            Permission.USER_VIEW_PROFILE,
            Permission.USER_UPDATE_PROFILE,
            Permission.USER_VIEW_CONTENT,
            Permission.COURSE_VIEW,
        ],
        "endpoints": [
            "GET /user/profile",
            "PUT /user/profile",
            "GET /user/content",
            "GET /courses",
        ]
    }
}

def get_permission_matrix():
    """Get the complete permission matrix for documentation"""
    return PERMISSION_MATRIX
```

## 🧪 Testing Requirements
- [ ] Test all endpoints with appropriate roles
- [ ] Test unauthorized access returns 403
- [ ] Test permission inheritance works correctly
- [ ] Test role-based access control
- [ ] Test context-aware permissions
- [ ] Test audit logging captures access attempts
- [ ] Performance test permission checking overhead

## 📊 Validation Checklist
- [ ] All endpoints have permission requirements
- [ ] Permission inheritance works correctly
- [ ] Unauthorized access is properly blocked
- [ ] Audit logging captures security events
- [ ] Performance overhead is minimal
- [ ] Error messages are appropriate
- [ ] Permission matrix is complete

## 🚨 Security Considerations
- [ ] No endpoints are unprotected
- [ ] Permission checking is comprehensive
- [ ] Role inheritance prevents privilege escalation
- [ ] Audit logging captures all access attempts
- [ ] Error messages don't leak information
- [ ] Performance doesn't compromise security

## 📈 Performance Considerations
- [ ] Permission checking overhead <10ms
- [ ] Efficient role and permission lookup
- [ ] Minimal database queries for authorization
- [ ] Caching of permission results where appropriate

## 🎯 Definition of Done
- [ ] All endpoints have appropriate permissions
- [ ] Role-based access control is enforced
- [ ] Permission inheritance works correctly
- [ ] Unauthorized access is properly blocked
- [ ] Audit logging captures security events
- [ ] Performance requirements are met
- [ ] Permission matrix is documented
- [ ] All tests pass
- [ ] Code review is completed
