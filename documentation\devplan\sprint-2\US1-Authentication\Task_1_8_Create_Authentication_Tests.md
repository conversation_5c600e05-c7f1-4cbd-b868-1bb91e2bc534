# Task 1.8: Create Authentication Tests

## 📋 Task Overview
**User Story:** HU-2.1 - JWT Authentication System  
**Task ID:** 1.8  
**Estimated Time:** 3 hours  
**Priority:** Critical  
**Complexity:** High  

## 🎯 Description
Implement comprehensive tests for authentication functionality including unit tests for security utilities, integration tests for authentication endpoints, test fixtures for users and authentication, and performance/security tests.

## 📦 Deliverables
- [ ] Create `backend/tests/test_auth.py`
- [ ] Unit tests for security utilities
- [ ] Integration tests for authentication endpoints
- [ ] Test fixtures for users and authentication
- [ ] Performance and security tests

## ✅ Acceptance Criteria
- [ ] Test password hashing and verification
- [ ] Test JWT token creation and validation
- [ ] Test user authentication with valid/invalid credentials
- [ ] Test authentication endpoints with various scenarios
- [ ] Test role-based access control
- [ ] Test tenant isolation in authentication
- [ ] Test error handling and edge cases
- [ ] All tests pass with >95% coverage

## 🔧 Technical Requirements

### Test Categories
1. **Unit Tests**
   - Security utilities (password hashing, JWT)
   - Authentication service methods
   - Model validation and properties

2. **Integration Tests**
   - Authentication API endpoints
   - Database operations
   - Error handling scenarios

3. **Security Tests**
   - Token validation and expiration
   - Password security requirements
   - Tenant isolation enforcement

4. **Performance Tests**
   - Authentication response times
   - Password hashing performance
   - Token validation speed

## 🔗 Dependencies
- **Prerequisite:** All authentication components completed
- **Testing:** pytest, pytest-asyncio, httpx for testing
- **Fixtures:** Database and user test fixtures

## 📝 Code Example
```python
import pytest
import asyncio
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from jose import jwt

from ..main import app
from ..core.config import settings
from ..core.security import (
    verify_password, get_password_hash, create_access_token, verify_token
)
from ..services.auth_service import AuthService
from ..schemas.auth import UserCreate, LoginRequest
from ..models.user import User
from ..models.tenant import Tenant

client = TestClient(app)

class TestSecurityUtilities:
    """Test security utility functions"""
    
    def test_password_hashing(self):
        """Test password hashing and verification"""
        password = "test_password_123"
        
        # Test hashing
        hashed = get_password_hash(password)
        assert hashed != password
        assert len(hashed) > 50  # bcrypt hashes are long
        
        # Test verification
        assert verify_password(password, hashed) is True
        assert verify_password("wrong_password", hashed) is False
    
    def test_password_hashing_uniqueness(self):
        """Test that same password produces different hashes"""
        password = "test_password_123"
        hash1 = get_password_hash(password)
        hash2 = get_password_hash(password)
        
        # Hashes should be different due to salt
        assert hash1 != hash2
        
        # Both should verify correctly
        assert verify_password(password, hash1) is True
        assert verify_password(password, hash2) is True
    
    def test_jwt_token_creation(self):
        """Test JWT token creation"""
        data = {
            "sub": "user_id_123",
            "tenant_id": "tenant_id_456",
            "role": "admin",
            "email": "<EMAIL>"
        }
        
        token = create_access_token(data)
        assert isinstance(token, str)
        assert len(token) > 100  # JWT tokens are long
        
        # Decode and verify
        payload = verify_token(token)
        assert payload["sub"] == data["sub"]
        assert payload["tenant_id"] == data["tenant_id"]
        assert payload["role"] == data["role"]
        assert payload["email"] == data["email"]
        assert "exp" in payload
        assert "iat" in payload
    
    def test_jwt_token_expiration(self):
        """Test JWT token expiration"""
        data = {"sub": "user_id_123"}
        
        # Create token with short expiration
        short_expiry = timedelta(seconds=1)
        token = create_access_token(data, expires_delta=short_expiry)
        
        # Token should be valid immediately
        payload = verify_token(token)
        assert payload["sub"] == data["sub"]
        
        # Wait for expiration
        import time
        time.sleep(2)
        
        # Token should now be expired
        with pytest.raises(Exception):  # Should raise HTTPException
            verify_token(token)
    
    def test_invalid_jwt_token(self):
        """Test invalid JWT token handling"""
        # Test completely invalid token
        with pytest.raises(Exception):
            verify_token("invalid_token")
        
        # Test token with wrong signature
        fake_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
        with pytest.raises(Exception):
            verify_token(fake_token)

class TestAuthService:
    """Test authentication service"""
    
    def test_create_user(self, test_db: Session, sample_tenant: Tenant):
        """Test user creation"""
        auth_service = AuthService(test_db)
        
        user_data = UserCreate(
            email="<EMAIL>",
            password="securepassword123",
            first_name="John",
            last_name="Doe",
            role="user",
            tenant_id=sample_tenant.id
        )
        
        user = auth_service.create_user(user_data)
        
        assert user.email == "<EMAIL>"
        assert user.first_name == "John"
        assert user.last_name == "Doe"
        assert user.role == "user"
        assert user.tenant_id == sample_tenant.id
        assert user.hashed_password != "securepassword123"  # Should be hashed
        assert verify_password("securepassword123", user.hashed_password)
    
    def test_create_duplicate_user(self, test_db: Session, sample_tenant: Tenant):
        """Test creating user with duplicate email"""
        auth_service = AuthService(test_db)
        
        user_data = UserCreate(
            email="<EMAIL>",
            password="securepassword123",
            tenant_id=sample_tenant.id
        )
        
        # Create first user
        auth_service.create_user(user_data)
        
        # Try to create duplicate
        with pytest.raises(Exception):  # Should raise HTTPException
            auth_service.create_user(user_data)
    
    def test_authenticate_user_success(self, test_db: Session, sample_user: User, sample_tenant: Tenant):
        """Test successful user authentication"""
        auth_service = AuthService(test_db)
        
        login_data = LoginRequest(
            email=sample_user.email,
            password="testpassword123",  # From fixture
            tenant_slug=sample_tenant.slug
        )
        
        authenticated_user = auth_service.authenticate_user(login_data)
        
        assert authenticated_user is not None
        assert authenticated_user.id == sample_user.id
        assert authenticated_user.login_count == sample_user.login_count + 1
        assert authenticated_user.last_login is not None
    
    def test_authenticate_user_invalid_password(self, test_db: Session, sample_user: User, sample_tenant: Tenant):
        """Test authentication with invalid password"""
        auth_service = AuthService(test_db)
        
        login_data = LoginRequest(
            email=sample_user.email,
            password="wrongpassword",
            tenant_slug=sample_tenant.slug
        )
        
        authenticated_user = auth_service.authenticate_user(login_data)
        assert authenticated_user is None
    
    def test_authenticate_user_invalid_email(self, test_db: Session, sample_tenant: Tenant):
        """Test authentication with invalid email"""
        auth_service = AuthService(test_db)
        
        login_data = LoginRequest(
            email="<EMAIL>",
            password="anypassword",
            tenant_slug=sample_tenant.slug
        )
        
        authenticated_user = auth_service.authenticate_user(login_data)
        assert authenticated_user is None
    
    def test_create_access_token_for_user(self, test_db: Session, sample_user: User):
        """Test access token creation for user"""
        auth_service = AuthService(test_db)
        
        token_response = auth_service.create_access_token_for_user(sample_user)
        
        assert "access_token" in token_response
        assert "token_type" in token_response
        assert "expires_in" in token_response
        assert "user" in token_response
        
        assert token_response["token_type"] == "bearer"
        assert token_response["expires_in"] == settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60
        assert token_response["user"].id == sample_user.id
        
        # Verify token is valid
        token = token_response["access_token"]
        payload = verify_token(token)
        assert payload["sub"] == sample_user.id
        assert payload["tenant_id"] == sample_user.tenant_id

class TestAuthenticationEndpoints:
    """Test authentication API endpoints"""
    
    def test_login_success(self, sample_user: User, sample_tenant: Tenant):
        """Test successful login"""
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": sample_user.email,
                "password": "testpassword123",
                "tenant_slug": sample_tenant.slug
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "access_token" in data
        assert "token_type" in data
        assert "expires_in" in data
        assert "user" in data
        
        assert data["token_type"] == "bearer"
        assert data["user"]["email"] == sample_user.email
    
    def test_login_invalid_credentials(self, sample_user: User, sample_tenant: Tenant):
        """Test login with invalid credentials"""
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": sample_user.email,
                "password": "wrongpassword",
                "tenant_slug": sample_tenant.slug
            }
        )
        
        assert response.status_code == 401
        assert "Incorrect email or password" in response.json()["detail"]
    
    def test_register_success(self, sample_tenant: Tenant):
        """Test successful user registration"""
        response = client.post(
            "/api/v1/auth/register",
            json={
                "email": "<EMAIL>",
                "password": "securepassword123",
                "first_name": "New",
                "last_name": "User",
                "tenant_id": sample_tenant.id
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["email"] == "<EMAIL>"
        assert data["first_name"] == "New"
        assert data["last_name"] == "User"
        assert "hashed_password" not in data  # Should not expose password
    
    def test_oauth2_token_endpoint(self, sample_user: User, sample_tenant: Tenant):
        """Test OAuth2 compatible token endpoint"""
        response = client.post(
            "/api/v1/auth/token",
            data={
                "username": sample_user.email,
                "password": "testpassword123",
                "client_id": sample_tenant.slug
            },
            headers={"Content-Type": "application/x-www-form-urlencoded"}
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "access_token" in data
        assert "token_type" in data
        assert data["token_type"] == "bearer"
    
    def test_protected_endpoint_with_token(self, auth_headers: dict):
        """Test accessing protected endpoint with valid token"""
        response = client.get(
            "/api/v1/auth/me",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "email" in data
    
    def test_protected_endpoint_without_token(self):
        """Test accessing protected endpoint without token"""
        response = client.get("/api/v1/auth/me")
        
        assert response.status_code == 401

class TestPerformance:
    """Test authentication performance"""
    
    def test_password_hashing_performance(self):
        """Test password hashing performance"""
        import time
        
        password = "test_password_123"
        start_time = time.time()
        
        # Hash password 10 times
        for _ in range(10):
            get_password_hash(password)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 10
        
        # Should take less than 200ms per hash
        assert avg_time < 0.2
    
    def test_token_validation_performance(self):
        """Test JWT token validation performance"""
        import time
        
        data = {"sub": "user_id_123"}
        token = create_access_token(data)
        
        start_time = time.time()
        
        # Validate token 100 times
        for _ in range(100):
            verify_token(token)
        
        end_time = time.time()
        avg_time = (end_time - start_time) / 100
        
        # Should take less than 10ms per validation
        assert avg_time < 0.01

# Test fixtures
@pytest.fixture
def sample_tenant(test_db: Session):
    """Create a sample tenant for testing"""
    tenant = Tenant(
        name="Test Tenant",
        slug="test-tenant",
        domain="test.example.com"
    )
    test_db.add(tenant)
    test_db.commit()
    test_db.refresh(tenant)
    return tenant

@pytest.fixture
def sample_user(test_db: Session, sample_tenant: Tenant):
    """Create a sample user for testing"""
    hashed_password = get_password_hash("testpassword123")
    user = User(
        email="<EMAIL>",
        hashed_password=hashed_password,
        first_name="Test",
        last_name="User",
        role="user",
        tenant_id=sample_tenant.id
    )
    test_db.add(user)
    test_db.commit()
    test_db.refresh(user)
    return user

@pytest.fixture
def auth_headers(sample_user: User):
    """Create authentication headers for testing"""
    auth_service = AuthService(test_db)
    token_response = auth_service.create_access_token_for_user(sample_user)
    return {"Authorization": f"Bearer {token_response['access_token']}"}
```

## 🎯 Definition of Done
- [ ] All authentication tests are implemented
- [ ] Unit tests cover security utilities
- [ ] Integration tests cover API endpoints
- [ ] Performance tests meet requirements
- [ ] Security tests validate token handling
- [ ] Test coverage >95%
- [ ] All tests pass consistently
- [ ] Code review is completed
