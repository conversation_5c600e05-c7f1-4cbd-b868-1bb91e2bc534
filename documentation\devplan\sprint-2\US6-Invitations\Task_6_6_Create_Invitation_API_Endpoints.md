# Task 6.6: Create Invitation API Endpoints

## 📋 Task Overview
**User Story:** HU-2.6 - Invitation System  
**Task ID:** 6.6  
**Estimated Time:** 3 hours  
**Priority:** High  
**Complexity:** High  

## 🎯 Description
Implement FastAPI endpoints for invitation management that provide invitation creation and sending, invitation acceptance workflow, invitation management operations, and proper authorization and error handling.

## 📦 Deliverables
- [ ] Create `backend/src/api/v1/endpoints/invitations.py`
- [ ] Invitation creation and bulk creation endpoints
- [ ] Invitation acceptance and validation endpoints
- [ ] Invitation management (list, update, cancel, resend)
- [ ] Proper authorization and error handling

## ✅ Acceptance Criteria
- [ ] POST /invitations creates and sends invitation
- [ ] POST /invitations/bulk creates multiple invitations
- [ ] GET /invitations/{token}/validate validates invitation token
- [ ] POST /invitations/{token}/accept accepts invitation and creates user
- [ ] GET /invitations lists tenant invitations with filtering
- [ ] PUT /invitations/{id} updates invitation
- [ ] DELETE /invitations/{id} cancels invitation
- [ ] POST /invitations/{id}/resend resends invitation
- [ ] All endpoints enforce proper role-based permissions
- [ ] Comprehensive error handling and validation

## 🔧 Technical Requirements

### Endpoint Categories
1. **Invitation Creation**
   - Single invitation creation
   - Bulk invitation creation
   - Email sending integration

2. **Invitation Acceptance**
   - Token validation
   - User account creation
   - Invitation marking as accepted

3. **Invitation Management**
   - List with filtering and pagination
   - Update invitation details
   - Cancel invitations
   - Resend invitations

### Security Requirements
- Role-based access control
- Token validation and security
- Input validation and sanitization
- Rate limiting for invitation creation

## 🔗 Dependencies
- **Prerequisite:** InvitationService (Task 6.3), Invitation schemas (Task 6.2)
- **Services:** InvitationService, EmailService
- **Authorization:** Admin role requirements

## 📝 Code Example
```python
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional

from ....core.database import get_db
from ....core.deps import get_current_admin_user, get_current_tenant
from ....models.user import User
from ....models.tenant import Tenant
from ....schemas.invitation import (
    InvitationCreate, InvitationInDB, InvitationAccept, InvitationUpdate,
    BulkInvitationCreate, BulkInvitationResult, InvitationValidation,
    InvitationListResponse, InvitationStats, InvitationResend
)
from ....services.invitation_service import InvitationService

router = APIRouter()

@router.post("/", response_model=InvitationInDB)
async def create_invitation(
    invitation_data: InvitationCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Create and send a new invitation"""
    invitation_service = InvitationService(db)
    
    try:
        invitation = invitation_service.create_invitation(
            invitation_data,
            current_tenant.id,
            current_user.id
        )
        return invitation
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create invitation: {str(e)}"
        )

@router.post("/bulk", response_model=BulkInvitationResult)
async def create_bulk_invitations(
    bulk_data: BulkInvitationCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Create multiple invitations at once"""
    invitation_service = InvitationService(db)
    
    try:
        result = invitation_service.bulk_create_invitations(
            bulk_data.invitations,
            current_tenant.id,
            current_user.id,
            send_emails=bulk_data.send_emails,
            background_tasks=background_tasks
        )
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create bulk invitations: {str(e)}"
        )

@router.get("/{token}/validate", response_model=InvitationValidation)
async def validate_invitation_token(
    token: str,
    db: Session = Depends(get_db)
):
    """Validate an invitation token"""
    invitation_service = InvitationService(db)
    
    try:
        validation_result = invitation_service.validate_invitation_token(token)
        return validation_result
    except Exception as e:
        return InvitationValidation(
            valid=False,
            error=f"Token validation failed: {str(e)}"
        )

@router.post("/{token}/accept", response_model=dict)
async def accept_invitation(
    token: str,
    acceptance_data: InvitationAccept,
    db: Session = Depends(get_db)
):
    """Accept an invitation and create user account"""
    invitation_service = InvitationService(db)
    
    try:
        user = invitation_service.accept_invitation(token, acceptance_data)
        return {
            "message": "Invitation accepted successfully",
            "user": {
                "id": user.id,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to accept invitation: {str(e)}"
        )

@router.get("/", response_model=InvitationListResponse)
async def get_invitations(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    role: Optional[str] = Query(None),
    email_search: Optional[str] = Query(None),
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get invitations for current tenant with filtering"""
    invitation_service = InvitationService(db)
    
    try:
        result = invitation_service.get_tenant_invitations(
            current_tenant.id,
            status=status,
            role=role,
            email_search=email_search,
            skip=skip,
            limit=limit
        )
        
        return InvitationListResponse(
            invitations=result["invitations"],
            total_count=result["total_count"],
            page=result["page"],
            pages=result["pages"],
            has_next=result["page"] < result["pages"],
            has_prev=result["page"] > 1
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve invitations: {str(e)}"
        )

@router.get("/{invitation_id}", response_model=InvitationInDB)
async def get_invitation(
    invitation_id: str,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get a specific invitation by ID"""
    invitation_service = InvitationService(db)
    
    invitation = invitation_service.get_invitation_by_id(invitation_id, current_tenant.id)
    if not invitation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invitation not found"
        )
    
    return invitation

@router.put("/{invitation_id}", response_model=InvitationInDB)
async def update_invitation(
    invitation_id: str,
    update_data: InvitationUpdate,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Update an invitation"""
    invitation_service = InvitationService(db)
    
    try:
        invitation = invitation_service.update_invitation(
            invitation_id,
            update_data,
            current_tenant.id
        )
        return invitation
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update invitation: {str(e)}"
        )

@router.delete("/{invitation_id}")
async def cancel_invitation(
    invitation_id: str,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Cancel an invitation"""
    invitation_service = InvitationService(db)
    
    try:
        success = invitation_service.cancel_invitation(invitation_id, current_tenant.id)
        if success:
            return {"message": "Invitation cancelled successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot cancel this invitation"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel invitation: {str(e)}"
        )

@router.post("/{invitation_id}/resend")
async def resend_invitation(
    invitation_id: str,
    resend_data: InvitationResend,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Resend an invitation"""
    invitation_service = InvitationService(db)
    
    try:
        success = invitation_service.resend_invitation(
            invitation_id,
            current_tenant.id,
            regenerate_token=resend_data.regenerate_token,
            extend_expiry=resend_data.extend_expiry,
            new_message=resend_data.new_message
        )
        
        if success:
            return {"message": "Invitation resent successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to resend invitation"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to resend invitation: {str(e)}"
        )

@router.get("/stats/overview", response_model=InvitationStats)
async def get_invitation_statistics(
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Get invitation statistics for the tenant"""
    invitation_service = InvitationService(db)
    
    try:
        stats = invitation_service.get_invitation_statistics(current_tenant.id)
        return stats
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve invitation statistics: {str(e)}"
        )

@router.post("/cleanup-expired")
async def cleanup_expired_invitations(
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Clean up expired invitations (system owner only)"""
    if current_user.role != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only system owners can perform cleanup operations"
        )
    
    invitation_service = InvitationService(db)
    
    try:
        cleaned_count = invitation_service.cleanup_expired_invitations()
        return {
            "message": f"Cleaned up {cleaned_count} expired invitations",
            "count": cleaned_count
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cleanup expired invitations: {str(e)}"
        )

# Public endpoint for invitation acceptance (no auth required)
public_router = APIRouter(prefix="/public")

@public_router.get("/invitations/{token}/info", response_model=InvitationValidation)
async def get_invitation_info(
    token: str,
    db: Session = Depends(get_db)
):
    """Get invitation information for public acceptance page"""
    invitation_service = InvitationService(db)
    
    try:
        validation_result = invitation_service.validate_invitation_token(token)
        return validation_result
    except Exception as e:
        return InvitationValidation(
            valid=False,
            error="Invalid or expired invitation"
        )

@public_router.post("/invitations/{token}/accept", response_model=dict)
async def accept_invitation_public(
    token: str,
    acceptance_data: InvitationAccept,
    db: Session = Depends(get_db)
):
    """Public endpoint for accepting invitations"""
    invitation_service = InvitationService(db)
    
    try:
        user = invitation_service.accept_invitation(token, acceptance_data)
        
        # Create access token for immediate login
        from ....services.auth_service import AuthService
        auth_service = AuthService(db)
        token_response = auth_service.create_access_token_for_user(user)
        
        return {
            "message": "Account created successfully",
            "user": {
                "id": user.id,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "role": user.role
            },
            "access_token": token_response["access_token"],
            "token_type": token_response["token_type"],
            "expires_in": token_response["expires_in"]
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to accept invitation: {str(e)}"
        )

# Rate limiting for invitation creation
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded

limiter = Limiter(key_func=get_remote_address)

@router.post("/", response_model=InvitationInDB)
@limiter.limit("10/minute")  # Limit invitation creation
async def create_invitation_with_rate_limit(
    request: Request,
    invitation_data: InvitationCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Create invitation with rate limiting"""
    return await create_invitation(
        invitation_data, background_tasks, current_user, current_tenant, db
    )
```

## 🧪 Testing Requirements
- [ ] Test invitation creation with valid data
- [ ] Test bulk invitation creation
- [ ] Test invitation token validation
- [ ] Test invitation acceptance workflow
- [ ] Test invitation management operations
- [ ] Test authorization for admin-only endpoints
- [ ] Test error handling for invalid data
- [ ] Test rate limiting for invitation creation

## 📊 Validation Checklist
- [ ] All endpoints have proper authorization
- [ ] Input validation prevents invalid data
- [ ] Error responses are helpful and secure
- [ ] Rate limiting prevents abuse
- [ ] Public endpoints are properly secured
- [ ] API documentation is complete
- [ ] Performance requirements are met

## 🚨 Security Considerations
- [ ] Admin role required for invitation management
- [ ] Token validation prevents unauthorized access
- [ ] Rate limiting prevents invitation spam
- [ ] Input validation prevents injection attacks
- [ ] Error messages don't leak sensitive information
- [ ] Public endpoints are properly secured

## 📈 Performance Considerations
- [ ] Invitation creation response time <200ms
- [ ] Bulk operations handle large datasets efficiently
- [ ] Database queries are optimized
- [ ] Background tasks for email sending
- [ ] Proper pagination for large lists

## 🎯 Definition of Done
- [ ] All invitation API endpoints are implemented
- [ ] Invitation creation and acceptance work correctly
- [ ] Invitation management operations are functional
- [ ] Authorization is properly enforced
- [ ] Error handling is comprehensive
- [ ] Rate limiting prevents abuse
- [ ] Public endpoints work for invitation acceptance
- [ ] All tests pass
- [ ] API documentation is complete
- [ ] Code review is completed
