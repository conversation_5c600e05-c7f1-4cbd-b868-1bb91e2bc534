# Task 1.4: Define Authentication Schemas

## 📋 Task Overview
**User Story:** HU-2.1 - JWT Authentication System  
**Task ID:** 1.4  
**Estimated Time:** 1.5 hours  
**Priority:** Critical  
**Complexity:** Low  

## 🎯 Description
Create Pydantic schemas for authentication requests and responses that provide proper validation, serialization, and API documentation for all authentication-related operations.

## 📦 Deliverables
- [ ] Create `backend/src/schemas/auth.py`
- [ ] User creation and update schemas
- [ ] Login request and token response schemas
- [ ] Password reset schemas
- [ ] Proper validation rules

## ✅ Acceptance Criteria
- [ ] UserCreate schema includes password validation (min 8 chars)
- [ ] UserUpdate schema allows partial updates
- [ ] LoginRequest supports email/password and optional tenant_slug
- [ ] Token schema includes access_token, token_type, expires_in, user
- [ ] Password reset schemas with secure token handling
- [ ] All schemas have proper validation and error messages

## 🔧 Technical Requirements

### Schema Categories
1. **User Management Schemas**
   - UserBase, UserCreate, UserUpdate, UserInDB
2. **Authentication Schemas**
   - LoginRequest, Token, TokenData
3. **Password Management Schemas**
   - PasswordResetRequest, PasswordReset

### Validation Rules
- Email format validation
- Password strength requirements (min 8 characters)
- Role validation (owner, admin, user)
- Optional field handling
- Proper error messages

## 🔗 Dependencies
- **Prerequisite:** Pydantic library available
- **Models:** User model structure for reference
- **Standards:** Email validation and password policies

## 🧪 Testing Requirements
- [ ] Test schema validation with valid data
- [ ] Test schema validation with invalid data
- [ ] Test password validation rules
- [ ] Test email format validation
- [ ] Test optional field handling
- [ ] Test error message clarity
- [ ] Test serialization/deserialization

## 📊 Validation Checklist
- [ ] All schemas follow naming conventions
- [ ] Validation rules are comprehensive
- [ ] Error messages are user-friendly
- [ ] Type hints are complete
- [ ] Documentation strings are clear
- [ ] Examples are provided for complex schemas

## 🚨 Security Considerations
- [ ] Password validation prevents weak passwords
- [ ] No sensitive data in response schemas
- [ ] Proper input sanitization
- [ ] Email validation prevents injection
- [ ] Token schemas don't expose secrets

## 📈 Performance Considerations
- [ ] Validation is fast and efficient
- [ ] Minimal memory usage
- [ ] No blocking operations
- [ ] Efficient serialization

## 🔄 Implementation Steps
1. **Create base user schemas**
2. **Add authentication request schemas**
3. **Create response schemas**
4. **Add password management schemas**
5. **Implement validation rules**
6. **Add comprehensive documentation**
7. **Write validation tests**

## 📝 Code Example
```python
from pydantic import BaseModel, EmailStr, validator
from typing import Optional
from datetime import datetime

class UserBase(BaseModel):
    email: EmailStr
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    is_active: bool = True
    role: str = "user"

class UserCreate(UserBase):
    password: str
    tenant_id: str
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        if not any(c.isalpha() for c in v):
            raise ValueError('Password must contain at least one letter')
        return v
    
    @validator('role')
    def validate_role(cls, v):
        allowed_roles = ['owner', 'admin', 'user']
        if v not in allowed_roles:
            raise ValueError(f'Role must be one of: {", ".join(allowed_roles)}')
        return v

class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    username: Optional[str] = None
    is_active: Optional[bool] = None
    role: Optional[str] = None
    
    @validator('role')
    def validate_role(cls, v):
        if v is not None:
            allowed_roles = ['owner', 'admin', 'user']
            if v not in allowed_roles:
                raise ValueError(f'Role must be one of: {", ".join(allowed_roles)}')
        return v

class UserInDB(UserBase):
    id: str
    tenant_id: str
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    login_count: int = 0
    
    class Config:
        from_attributes = True

class LoginRequest(BaseModel):
    email: EmailStr
    password: str
    tenant_slug: Optional[str] = None
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "securepassword123",
                "tenant_slug": "my-organization"
            }
        }

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserInDB
    
    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 86400,
                "user": {
                    "id": "123e4567-e89b-12d3-a456-426614174000",
                    "email": "<EMAIL>",
                    "role": "user"
                }
            }
        }

class TokenData(BaseModel):
    user_id: Optional[str] = None
    tenant_id: Optional[str] = None

class PasswordResetRequest(BaseModel):
    email: EmailStr
    tenant_slug: str
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "tenant_slug": "my-organization"
            }
        }

class PasswordReset(BaseModel):
    token: str
    new_password: str
    
    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        if not any(c.isalpha() for c in v):
            raise ValueError('Password must contain at least one letter')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "token": "secure-reset-token-here",
                "new_password": "newsecurepassword123"
            }
        }
```

## 🔍 Validation Examples
```python
def test_user_create_validation():
    """Test UserCreate schema validation"""
    # Valid data
    valid_data = {
        "email": "<EMAIL>",
        "password": "securepass123",
        "tenant_id": "tenant-uuid",
        "first_name": "John",
        "last_name": "Doe"
    }
    user = UserCreate(**valid_data)
    assert user.email == "<EMAIL>"
    
    # Invalid password (too short)
    invalid_data = valid_data.copy()
    invalid_data["password"] = "short"
    
    with pytest.raises(ValueError, match="at least 8 characters"):
        UserCreate(**invalid_data)

def test_login_request_validation():
    """Test LoginRequest schema validation"""
    # Valid data
    valid_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    login = LoginRequest(**valid_data)
    assert login.email == "<EMAIL>"
    
    # Invalid email
    invalid_data = {
        "email": "invalid-email",
        "password": "password123"
    }
    
    with pytest.raises(ValueError, match="email"):
        LoginRequest(**invalid_data)
```

## 🎯 Definition of Done
- [ ] All authentication schemas are implemented
- [ ] Validation rules work correctly
- [ ] Error messages are clear and helpful
- [ ] All schemas have proper documentation
- [ ] Examples are provided for API documentation
- [ ] Tests cover all validation scenarios
- [ ] Code review is completed
- [ ] Integration with API endpoints is verified
