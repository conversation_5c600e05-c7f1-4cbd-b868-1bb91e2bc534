# Task 2.4: Add Tenant Customization Features

## 📋 Task Overview
**User Story:** HU-2.2 - Tenant CRUD Operations  
**Task ID:** 2.4  
**Estimated Time:** 2 hours  
**Priority:** High  
**Complexity:** Medium  

## 🎯 Description
Implement tenant branding and customization capabilities including logo management, color scheme customization, tenant settings management, and default configuration setup.

## 📦 Deliverables
- [ ] Add branding fields to Tenant model (if not exists)
- [ ] Logo upload and management functionality
- [ ] Color scheme customization
- [ ] Tenant settings management
- [ ] Default configuration setup

## ✅ Acceptance Criteria
- [ ] Tenant model includes logo_url, primary_color, secondary_color
- [ ] Settings field stores custom configuration as JSONB
- [ ] Default branding values are set during tenant creation
- [ ] Logo upload endpoint with file validation
- [ ] Color validation for hex color codes
- [ ] Settings schema validation for known configuration keys
- [ ] Tenant customization is isolated per tenant

## 🔧 Technical Requirements

### Branding Fields
- logo_url: String field for logo image URL
- primary_color: Hex color code for primary branding
- secondary_color: Hex color code for secondary branding
- settings: JSONB field for flexible configuration

### File Upload Support
- Logo upload with size and format validation
- Secure file storage and URL generation
- Image optimization and resizing

### Settings Structure
```json
{
  "branding": {
    "logo_url": "https://example.com/logo.png",
    "primary_color": "#007bff",
    "secondary_color": "#6c757d",
    "custom_css": "/* custom styles */"
  },
  "features": {
    "advanced_analytics": true,
    "custom_domains": false,
    "api_access": true
  },
  "notifications": {
    "email_enabled": true,
    "slack_webhook": "https://hooks.slack.com/...",
    "notification_frequency": "daily"
  }
}
```

## 🔗 Dependencies
- **Prerequisite:** Tenant model and service
- **Storage:** File storage system for logo uploads
- **Validation:** Color and file validation utilities

## 📝 Code Example
```python
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlalchemy.orm import Session
import re
from typing import Dict, Any

from ....core.database import get_db
from ....core.deps import get_current_admin_user, get_current_tenant
from ....models.user import User
from ....models.tenant import Tenant
from ....services.tenant_service import TenantService
from ....core.file_storage import FileStorageService

router = APIRouter()

@router.post("/logo")
async def upload_tenant_logo(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Upload tenant logo"""
    # Validate file type
    allowed_types = ["image/jpeg", "image/png", "image/gif", "image/webp"]
    if file.content_type not in allowed_types:
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed."
        )
    
    # Validate file size (max 5MB)
    max_size = 5 * 1024 * 1024  # 5MB
    file_content = await file.read()
    if len(file_content) > max_size:
        raise HTTPException(
            status_code=400,
            detail="File too large. Maximum size is 5MB."
        )
    
    # Upload file and get URL
    file_storage = FileStorageService()
    logo_url = await file_storage.upload_file(
        file_content,
        f"tenants/{current_tenant.id}/logo",
        file.content_type
    )
    
    # Update tenant with new logo URL
    tenant_service = TenantService(db)
    from ....schemas.tenant import TenantUpdate
    update_data = TenantUpdate(logo_url=logo_url)
    updated_tenant = tenant_service.update_tenant(current_tenant.id, update_data)
    
    return {"logo_url": logo_url}

@router.put("/branding")
async def update_tenant_branding(
    branding_data: Dict[str, Any],
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Update tenant branding"""
    # Validate colors if provided
    if "primary_color" in branding_data:
        if not _validate_hex_color(branding_data["primary_color"]):
            raise HTTPException(
                status_code=400,
                detail="Invalid primary color format. Use hex format like #FF0000"
            )
    
    if "secondary_color" in branding_data:
        if not _validate_hex_color(branding_data["secondary_color"]):
            raise HTTPException(
                status_code=400,
                detail="Invalid secondary color format. Use hex format like #FF0000"
            )
    
    # Update tenant branding
    tenant_service = TenantService(db)
    from ....schemas.tenant import TenantUpdate
    update_data = TenantUpdate(**branding_data)
    updated_tenant = tenant_service.update_tenant(current_tenant.id, update_data)
    
    return {
        "primary_color": updated_tenant.primary_color,
        "secondary_color": updated_tenant.secondary_color,
        "logo_url": updated_tenant.logo_url
    }

@router.put("/settings")
async def update_tenant_settings(
    settings_data: Dict[str, Any],
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Update tenant settings"""
    # Validate settings structure
    validated_settings = _validate_tenant_settings(settings_data)
    
    # Merge with existing settings
    current_settings = current_tenant.settings or {}
    current_settings.update(validated_settings)
    
    # Update tenant
    tenant_service = TenantService(db)
    from ....schemas.tenant import TenantUpdate
    update_data = TenantUpdate(settings=current_settings)
    updated_tenant = tenant_service.update_tenant(current_tenant.id, update_data)
    
    return {"settings": updated_tenant.settings}

@router.get("/settings")
async def get_tenant_settings(
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Get tenant settings"""
    return {"settings": current_tenant.settings or {}}

def _validate_hex_color(color: str) -> bool:
    """Validate hex color format"""
    pattern = r'^#[0-9A-Fa-f]{6}$'
    return bool(re.match(pattern, color))

def _validate_tenant_settings(settings: Dict[str, Any]) -> Dict[str, Any]:
    """Validate tenant settings structure"""
    validated = {}
    
    # Validate branding settings
    if "branding" in settings:
        branding = settings["branding"]
        validated["branding"] = {}
        
        if "logo_url" in branding:
            # Validate URL format
            url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
            if re.match(url_pattern, branding["logo_url"]):
                validated["branding"]["logo_url"] = branding["logo_url"]
        
        if "primary_color" in branding:
            if _validate_hex_color(branding["primary_color"]):
                validated["branding"]["primary_color"] = branding["primary_color"]
        
        if "secondary_color" in branding:
            if _validate_hex_color(branding["secondary_color"]):
                validated["branding"]["secondary_color"] = branding["secondary_color"]
        
        if "custom_css" in branding:
            # Basic CSS validation (could be more sophisticated)
            if isinstance(branding["custom_css"], str) and len(branding["custom_css"]) < 10000:
                validated["branding"]["custom_css"] = branding["custom_css"]
    
    # Validate feature settings
    if "features" in settings:
        features = settings["features"]
        validated["features"] = {}
        
        allowed_features = [
            "advanced_analytics", "custom_domains", "api_access",
            "bulk_operations", "export_data", "custom_roles"
        ]
        
        for feature in allowed_features:
            if feature in features and isinstance(features[feature], bool):
                validated["features"][feature] = features[feature]
    
    # Validate notification settings
    if "notifications" in settings:
        notifications = settings["notifications"]
        validated["notifications"] = {}
        
        if "email_enabled" in notifications:
            if isinstance(notifications["email_enabled"], bool):
                validated["notifications"]["email_enabled"] = notifications["email_enabled"]
        
        if "slack_webhook" in notifications:
            # Basic webhook URL validation
            webhook_pattern = r'^https://hooks\.slack\.com/.*$'
            if re.match(webhook_pattern, notifications["slack_webhook"]):
                validated["notifications"]["slack_webhook"] = notifications["slack_webhook"]
        
        if "notification_frequency" in notifications:
            allowed_frequencies = ["immediate", "hourly", "daily", "weekly"]
            if notifications["notification_frequency"] in allowed_frequencies:
                validated["notifications"]["notification_frequency"] = notifications["notification_frequency"]
    
    return validated

# Default tenant settings
DEFAULT_TENANT_SETTINGS = {
    "branding": {
        "primary_color": "#007bff",
        "secondary_color": "#6c757d"
    },
    "features": {
        "advanced_analytics": False,
        "custom_domains": False,
        "api_access": True,
        "bulk_operations": True,
        "export_data": True,
        "custom_roles": False
    },
    "notifications": {
        "email_enabled": True,
        "notification_frequency": "daily"
    }
}
```

## 🎯 Definition of Done
- [ ] Tenant branding fields are implemented
- [ ] Logo upload functionality works
- [ ] Color validation is implemented
- [ ] Settings management is functional
- [ ] Default configurations are set
- [ ] All tests pass
- [ ] Code review is completed
