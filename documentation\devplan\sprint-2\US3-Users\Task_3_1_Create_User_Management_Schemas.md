# Task 3.1: Create User Management Schemas

## 📋 Task Overview
**User Story:** HU-2.3 - User Management System  
**Task ID:** 3.1  
**Estimated Time:** 2 hours  
**Priority:** Critical  
**Complexity:** Medium  

## 🎯 Description
Define comprehensive schemas for user management operations that provide proper validation, serialization, and API documentation for all user-related operations including search, bulk operations, and activity tracking.

## 📦 Deliverables
- [ ] Create `backend/src/schemas/user.py`
- [ ] User creation and update schemas
- [ ] User search and filtering schemas
- [ ] Bulk operation schemas
- [ ] User profile and activity schemas

## ✅ Acceptance Criteria
- [ ] UserCreate schema with role assignment and validation
- [ ] UserUpdate schema for partial profile updates
- [ ] UserSearch schema with filtering and pagination
- [ ] BulkUserCreate schema for CSV imports
- [ ] UserActivity schema for tracking user actions
- [ ] UserProfile schema for detailed user information
- [ ] Proper validation for email formats and roles
- [ ] Error handling for invalid user data

## 🔧 Technical Requirements

### Schema Categories
1. **Core User Schemas**
   - UserBase, UserCreate, UserUpdate, UserInDB
2. **Search and Filtering**
   - UserSearch, UserFilter, UserSort
3. **Bulk Operations**
   - BulkUserCreate, BulkUserUpdate, BulkOperationResult
4. **Activity and Profile**
   - UserActivity, UserProfile, UserStats

### Validation Rules
- Email format and uniqueness within tenant
- Password strength requirements
- Role validation (owner, admin, user)
- Optional field handling
- Bulk operation limits

## 📝 Code Example
```python
from pydantic import BaseModel, EmailStr, validator
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class UserRole(str, Enum):
    OWNER = "owner"
    ADMIN = "admin"
    USER = "user"

class UserStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"
    SUSPENDED = "suspended"

class UserBase(BaseModel):
    email: EmailStr
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    role: UserRole = UserRole.USER
    is_active: bool = True

class UserCreate(UserBase):
    password: str
    tenant_id: Optional[str] = None  # Set by service if not provided
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        if not any(c.isalpha() for c in v):
            raise ValueError('Password must contain at least one letter')
        return v
    
    @validator('username')
    def validate_username(cls, v):
        if v is not None:
            if len(v) < 3:
                raise ValueError('Username must be at least 3 characters long')
            if not v.replace('_', '').replace('-', '').isalnum():
                raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
        return v

class UserUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    username: Optional[str] = None
    is_active: Optional[bool] = None
    role: Optional[UserRole] = None
    bio: Optional[str] = None
    timezone: Optional[str] = None
    language: Optional[str] = None
    
    @validator('username')
    def validate_username(cls, v):
        if v is not None:
            if len(v) < 3:
                raise ValueError('Username must be at least 3 characters long')
            if not v.replace('_', '').replace('-', '').isalnum():
                raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
        return v

class UserInDB(UserBase):
    id: str
    tenant_id: str
    is_verified: bool = False
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    login_count: int = 0
    avatar_url: Optional[str] = None
    bio: Optional[str] = None
    timezone: str = "UTC"
    language: str = "en"
    
    class Config:
        from_attributes = True

class UserProfile(UserInDB):
    """Extended user profile with additional information"""
    full_name: Optional[str] = None
    is_owner: bool = False
    is_admin: bool = False
    tenant_name: Optional[str] = None
    
    @validator('full_name', pre=True, always=True)
    def set_full_name(cls, v, values):
        if v is not None:
            return v
        first_name = values.get('first_name')
        last_name = values.get('last_name')
        if first_name and last_name:
            return f"{first_name} {last_name}"
        return first_name or last_name or values.get('username') or values.get('email')

class UserSearch(BaseModel):
    """Schema for user search and filtering"""
    search_term: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    last_login_after: Optional[datetime] = None
    last_login_before: Optional[datetime] = None
    sort_by: Optional[str] = "created_at"
    sort_order: Optional[str] = "desc"
    
    @validator('sort_by')
    def validate_sort_by(cls, v):
        allowed_fields = [
            'created_at', 'updated_at', 'last_login', 'email', 
            'first_name', 'last_name', 'username', 'login_count'
        ]
        if v not in allowed_fields:
            raise ValueError(f'Sort field must be one of: {", ".join(allowed_fields)}')
        return v
    
    @validator('sort_order')
    def validate_sort_order(cls, v):
        if v not in ['asc', 'desc']:
            raise ValueError('Sort order must be "asc" or "desc"')
        return v

class BulkUserCreate(BaseModel):
    """Schema for bulk user creation"""
    users: List[UserCreate]
    send_invitations: bool = True
    
    @validator('users')
    def validate_users_limit(cls, v):
        if len(v) > 1000:
            raise ValueError('Cannot create more than 1000 users at once')
        if len(v) == 0:
            raise ValueError('At least one user must be provided')
        return v

class BulkUserUpdate(BaseModel):
    """Schema for bulk user updates"""
    user_ids: List[str]
    updates: UserUpdate
    
    @validator('user_ids')
    def validate_user_ids_limit(cls, v):
        if len(v) > 1000:
            raise ValueError('Cannot update more than 1000 users at once')
        if len(v) == 0:
            raise ValueError('At least one user ID must be provided')
        return v

class BulkOperationResult(BaseModel):
    """Result of bulk operations"""
    total: int
    successful: int
    failed: int
    errors: List[Dict[str, Any]] = []
    
    class Config:
        schema_extra = {
            "example": {
                "total": 100,
                "successful": 95,
                "failed": 5,
                "errors": [
                    {
                        "index": 10,
                        "email": "invalid@email",
                        "error": "Invalid email format"
                    }
                ]
            }
        }

class UserActivity(BaseModel):
    """Schema for user activity tracking"""
    id: str
    user_id: str
    action: str
    resource_type: Optional[str] = None
    resource_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class UserStats(BaseModel):
    """Schema for user statistics"""
    total_users: int
    active_users: int
    inactive_users: int
    pending_users: int
    users_by_role: Dict[str, int]
    recent_registrations: int
    recent_logins: int
    
    class Config:
        schema_extra = {
            "example": {
                "total_users": 150,
                "active_users": 140,
                "inactive_users": 10,
                "pending_users": 5,
                "users_by_role": {
                    "owner": 1,
                    "admin": 5,
                    "user": 144
                },
                "recent_registrations": 12,
                "recent_logins": 89
            }
        }

class UserListResponse(BaseModel):
    """Response schema for user list with pagination"""
    users: List[UserInDB]
    total_count: int
    page: int
    pages: int
    has_next: bool
    has_prev: bool
    
    class Config:
        schema_extra = {
            "example": {
                "users": [],
                "total_count": 150,
                "page": 1,
                "pages": 15,
                "has_next": True,
                "has_prev": False
            }
        }
```

## 🎯 Definition of Done
- [ ] All user management schemas are implemented
- [ ] Validation rules work correctly
- [ ] Search and filtering schemas are complete
- [ ] Bulk operation schemas handle limits
- [ ] Activity tracking schemas are functional
- [ ] Error messages are clear and helpful
- [ ] All tests pass
- [ ] Code review is completed
