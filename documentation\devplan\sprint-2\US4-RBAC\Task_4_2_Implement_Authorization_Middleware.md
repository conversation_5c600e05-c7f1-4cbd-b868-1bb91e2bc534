# Task 4.2: Implement Authorization Middleware

## 📋 Task Overview
**User Story:** HU-2.4 - Role-Based Access Control  
**Task ID:** 4.2  
**Estimated Time:** 3 hours  
**Priority:** Critical  
**Complexity:** High  

## 🎯 Description
Create middleware for enforcing role-based permissions that provides decorators for endpoint protection, role validation middleware, request context for permissions, and error handling for unauthorized access.

## 📦 Deliverables
- [ ] Create `backend/src/middleware/auth_middleware.py`
- [ ] Permission enforcement decorators
- [ ] Role validation middleware
- [ ] Request context for permissions
- [ ] Error handling for unauthorized access

## ✅ Acceptance Criteria
- [ ] @require_permission decorator for endpoint protection
- [ ] @require_role decorator for role-based access
- [ ] Middleware validates user permissions on each request
- [ ] Request context includes user role and permissions
- [ ] Proper HTTP 403 responses for unauthorized access
- [ ] Permission checking is efficient and cached
- [ ] Middleware integrates with FastAPI dependency system

## 🔧 Technical Requirements

### Middleware Components
1. **Permission Decorators**
   - @require_permission for specific permissions
   - @require_role for role-based access
   - @require_owner for system owner only

2. **Context Management**
   - Request-scoped permission context
   - User role and permission caching
   - Tenant-aware permission checking

3. **Error Handling**
   - Standardized 403 responses
   - Detailed error messages for debugging
   - Security-conscious public messages

## 🔗 Dependencies
- **Prerequisite:** Permission system architecture (Task 4.1)
- **Authentication:** User authentication dependencies
- **Models:** User model with role field

## 📝 Code Example
```python
from functools import wraps
from typing import List, Optional, Callable, Any
from fastapi import HTTPException, status, Request, Depends
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session

from ..core.permissions import Permission, Role, PermissionSystem, PermissionContext
from ..core.deps import get_current_user, get_current_active_user
from ..models.user import User
from ..core.database import get_db

security = HTTPBearer()

class AuthorizationMiddleware:
    """Middleware for handling authorization and permissions"""
    
    def __init__(self):
        self.permission_cache = {}
    
    def get_user_permissions(self, user: User) -> set:
        """Get cached user permissions"""
        cache_key = f"{user.id}:{user.role}:{user.updated_at}"
        
        if cache_key not in self.permission_cache:
            user_role = Role(user.role)
            permissions = PermissionSystem.get_role_permissions(user_role)
            self.permission_cache[cache_key] = permissions
        
        return self.permission_cache[cache_key]
    
    def check_permission(self, user: User, permission: Permission) -> bool:
        """Check if user has specific permission"""
        user_role = Role(user.role)
        return PermissionSystem.has_permission(user_role, permission)
    
    def check_role(self, user: User, required_role: Role) -> bool:
        """Check if user has required role or higher"""
        user_role = Role(user.role)
        return PermissionSystem.has_role_or_higher(user_role, required_role)

# Global middleware instance
auth_middleware = AuthorizationMiddleware()

def require_permission(permission: Permission):
    """Decorator to require specific permission"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract current user from kwargs (injected by dependency)
            current_user = None
            for key, value in kwargs.items():
                if isinstance(value, User):
                    current_user = value
                    break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if not auth_middleware.check_permission(current_user, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission '{permission.value}' required",
                    headers={"X-Required-Permission": permission.value}
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def require_role(role: Role):
    """Decorator to require specific role or higher"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            current_user = None
            for key, value in kwargs.items():
                if isinstance(value, User):
                    current_user = value
                    break
            
            if not current_user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if not auth_middleware.check_role(current_user, role):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Role '{role.value}' or higher required",
                    headers={"X-Required-Role": role.value}
                )
            
            return await func(*args, **kwargs)
        return wrapper
    return decorator

def require_owner():
    """Decorator to require system owner role"""
    return require_role(Role.OWNER)

def require_admin():
    """Decorator to require admin role or higher"""
    return require_role(Role.ADMIN)

# FastAPI dependency for permission checking
def require_permissions(permissions: List[Permission]):
    """FastAPI dependency to require multiple permissions"""
    async def permission_checker(current_user: User = Depends(get_current_active_user)):
        for permission in permissions:
            if not auth_middleware.check_permission(current_user, permission):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Permission '{permission.value}' required"
                )
        return current_user
    return permission_checker

def require_any_permission(permissions: List[Permission]):
    """FastAPI dependency to require any of the specified permissions"""
    async def permission_checker(current_user: User = Depends(get_current_active_user)):
        has_permission = any(
            auth_middleware.check_permission(current_user, perm) 
            for perm in permissions
        )
        
        if not has_permission:
            permission_names = [perm.value for perm in permissions]
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"One of these permissions required: {', '.join(permission_names)}"
            )
        return current_user
    return permission_checker

# Context-aware permission checking
class RequestPermissionContext:
    """Context for request-specific permission checking"""
    
    def __init__(self, user: User, tenant_id: str, request: Request):
        self.user = user
        self.tenant_id = tenant_id
        self.request = request
        self.permission_context = PermissionContext(
            user_role=Role(user.role),
            tenant_id=tenant_id
        )
    
    def can_access_tenant(self, target_tenant_id: str) -> bool:
        """Check if user can access specific tenant"""
        return self.permission_context.can_access_tenant(target_tenant_id)
    
    def can_manage_user(self, target_user: User) -> bool:
        """Check if user can manage another user"""
        target_role = Role(target_user.role)
        return self.permission_context.can_manage_user(target_role)
    
    def can_access_resource(self, resource_type: str, resource_id: str) -> bool:
        """Check if user can access specific resource"""
        # Implement resource-specific access control
        # This would be extended based on specific resource types
        return True

def get_permission_context(
    current_user: User = Depends(get_current_active_user),
    request: Request = None
) -> RequestPermissionContext:
    """Get permission context for current request"""
    return RequestPermissionContext(
        user=current_user,
        tenant_id=current_user.tenant_id,
        request=request
    )

# Utility functions for manual permission checking
def check_user_permission(user: User, permission: Permission) -> bool:
    """Utility function to check user permission"""
    return auth_middleware.check_permission(user, permission)

def check_user_role(user: User, role: Role) -> bool:
    """Utility function to check user role"""
    return auth_middleware.check_role(user, role)

def get_user_permissions(user: User) -> List[str]:
    """Get list of user permissions"""
    permissions = auth_middleware.get_user_permissions(user)
    return [perm.value for perm in permissions]

# Exception handlers for authorization errors
class InsufficientPermissionError(Exception):
    """Raised when user lacks required permission"""
    def __init__(self, permission: Permission, user_role: Role):
        self.permission = permission
        self.user_role = user_role
        super().__init__(f"Role '{user_role.value}' lacks permission '{permission.value}'")

class InsufficientRoleError(Exception):
    """Raised when user lacks required role"""
    def __init__(self, required_role: Role, user_role: Role):
        self.required_role = required_role
        self.user_role = user_role
        super().__init__(f"Role '{user_role.value}' insufficient, '{required_role.value}' required")

# Audit logging for permission checks
import logging
from datetime import datetime

permission_logger = logging.getLogger("permissions")

def log_permission_check(
    user: User,
    permission: Permission,
    granted: bool,
    resource_type: Optional[str] = None,
    resource_id: Optional[str] = None
):
    """Log permission check for audit purposes"""
    permission_logger.info(
        f"Permission check: user={user.id} role={user.role} "
        f"permission={permission.value} granted={granted} "
        f"resource_type={resource_type} resource_id={resource_id} "
        f"timestamp={datetime.utcnow().isoformat()}"
    )

def log_authorization_failure(
    user: User,
    required_permission: Permission,
    endpoint: str,
    ip_address: Optional[str] = None
):
    """Log authorization failure for security monitoring"""
    permission_logger.warning(
        f"Authorization failed: user={user.id} role={user.role} "
        f"required_permission={required_permission.value} endpoint={endpoint} "
        f"ip_address={ip_address} timestamp={datetime.utcnow().isoformat()}"
    )
```

## 🔍 Usage Examples
```python
from fastapi import APIRouter, Depends
from ..middleware.auth_middleware import require_permission, require_role, require_owner

router = APIRouter()

# Using permission decorator
@router.post("/users")
@require_permission(Permission.TENANT_MANAGE_USERS)
async def create_user(user_data: UserCreate, current_user: User = Depends(get_current_user)):
    # Only users with TENANT_MANAGE_USERS permission can access
    pass

# Using role decorator
@router.get("/admin/tenants")
@require_owner()
async def get_all_tenants(current_user: User = Depends(get_current_user)):
    # Only system owners can access
    pass

# Using FastAPI dependency
@router.get("/protected")
async def protected_endpoint(
    current_user: User = Depends(require_permissions([Permission.USER_VIEW_CONTENT]))
):
    # User must have USER_VIEW_CONTENT permission
    pass

# Using context-aware checking
@router.get("/tenant/{tenant_id}/data")
async def get_tenant_data(
    tenant_id: str,
    context: RequestPermissionContext = Depends(get_permission_context)
):
    if not context.can_access_tenant(tenant_id):
        raise HTTPException(status_code=403, detail="Cannot access this tenant")
    # Process request
    pass
```

## 🎯 Definition of Done
- [ ] Authorization middleware is fully implemented
- [ ] Permission decorators work correctly
- [ ] Role-based access control is enforced
- [ ] Request context provides permission checking
- [ ] Error handling is comprehensive
- [ ] Performance is optimized (<10ms overhead)
- [ ] Audit logging is implemented
- [ ] All tests pass
- [ ] Code review is completed
