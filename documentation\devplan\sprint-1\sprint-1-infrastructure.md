# Sprint 1: Base Infrastructure Implementation Guide

## Overview
This sprint establishes the foundational infrastructure for the ArroyoUniversity platform, including development environment, multi-tenant architecture, and PostgreSQL database with Row Level Security.

## Timeline: 2 weeks

## Prerequisites
- Docker Desktop installed and running
- Node.js 18+ and Python 3.11+
- Git configured
- VS Code or preferred IDE

## Step 1: Project Structure Setup

### 1.1 Create Root Directory Structure
```bash
mkdir ArroyoUniversity
cd ArroyoUniversity

# Create main directories
mkdir -p {frontend,backend,database,docker,scripts,docs}
mkdir -p backend/{src,tests,migrations}
mkdir -p frontend/{src,public,tests}
mkdir -p database/{migrations,seeds,schemas}
mkdir -p docker/{development,production}
```

### 1.2 Initialize Git Repository
```bash
git init
echo "node_modules/
*.env
*.log
.DS_Store
__pycache__/
*.pyc
.pytest_cache/
.coverage
dist/
build/" > .gitignore

git add .
git commit -m "Initial project structure"
```

## Step 2: Docker Development Environment

### 2.1 Create Docker Compose Configuration
Create `docker-compose.yml`:
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: arroyo_postgres
    environment:
      POSTGRES_DB: arroyo_university
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres_dev_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - arroyo_network

  redis:
    image: redis:7-alpine
    container_name: arroyo_redis
    ports:
      - "6379:6379"
    networks:
      - arroyo_network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: arroyo_backend
    environment:
      - DATABASE_URL=*********************************************************/arroyo_university
      - REDIS_URL=redis://redis:6379
      - ENVIRONMENT=development
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
    networks:
      - arroyo_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: arroyo_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - arroyo_network

volumes:
  postgres_data:

networks:
  arroyo_network:
    driver: bridge
```

### 2.2 Create Backend Dockerfile
Create `backend/Dockerfile.dev`:
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
```

### 2.3 Create Frontend Dockerfile
Create `frontend/Dockerfile.dev`:
```dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./
RUN npm install

# Copy application code
COPY . .

EXPOSE 3000

CMD ["npm", "start"]
```

## Step 3: Backend Foundation (FastAPI + SQLAlchemy)

### 3.1 Create Backend Requirements
Create `backend/requirements.txt`:
```
fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
pydantic==2.5.0
pydantic-settings==2.1.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
redis==5.0.1
celery==5.3.4
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
```

### 3.2 Create Main Application Structure
Create `backend/src/main.py`:
```python
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import time
import logging

from .core.config import settings
from .core.database import engine
from .models import Base
from .api.v1.api import api_router

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="ArroyoUniversity Learning Platform API",
    openapi_url=f"{settings.API_V1_STR}/openapi.json"
)

# Security middleware
app.add_middleware(TrustedHostMiddleware, allowed_hosts=settings.ALLOWED_HOSTS)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request timing middleware
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

@app.on_event("startup")
async def startup_event():
    logger.info("Starting ArroyoUniversity API...")
    # Create database tables
    Base.metadata.create_all(bind=engine)

@app.on_event("shutdown")
async def shutdown_event():
    logger.info("Shutting down ArroyoUniversity API...")

@app.get("/")
async def root():
    return {"message": "ArroyoUniversity API", "version": settings.VERSION}

@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": time.time()}
```

### 3.3 Create Configuration Management
Create `backend/src/core/config.py`:
```python
from pydantic_settings import BaseSettings
from typing import List, Optional
import os

class Settings(BaseSettings):
    PROJECT_NAME: str = "ArroyoUniversity"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # Database
    DATABASE_URL: str = os.getenv(
        "DATABASE_URL",
        "postgresql://postgres:postgres_dev_password@localhost:5432/arroyo_university"
    )

    # Redis
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379")

    # Security
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # CORS
    ALLOWED_ORIGINS: List[str] = ["http://localhost:3000", "http://127.0.0.1:3000"]
    ALLOWED_HOSTS: List[str] = ["localhost", "127.0.0.1"]

    # Environment
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")
    DEBUG: bool = ENVIRONMENT == "development"

    # OpenAI (for later sprints)
    OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")

    # Email (for later sprints)
    SMTP_HOST: Optional[str] = os.getenv("SMTP_HOST")
    SMTP_PORT: int = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USER: Optional[str] = os.getenv("SMTP_USER")
    SMTP_PASSWORD: Optional[str] = os.getenv("SMTP_PASSWORD")

    class Config:
        case_sensitive = True

settings = Settings()
```

### 3.4 Create Database Configuration
Create `backend/src/core/database.py`:
```python
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import logging

from .config import settings

logger = logging.getLogger(__name__)

# Create engine with connection pooling
engine = create_engine(
    settings.DATABASE_URL,
    poolclass=StaticPool,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=settings.DEBUG
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Custom metadata with naming convention for constraints
convention = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

metadata = MetaData(naming_convention=convention)
Base = declarative_base(metadata=metadata)

# Dependency for getting database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

# Test database connection
def test_db_connection():
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        logger.info("Database connection successful")
        return True
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        return False
```

## Step 4: Multi-Tenant Architecture Implementation

### 4.1 Create Base Models
Create `backend/src/models/__init__.py`:
```python
from .base import Base
from .tenant import Tenant
from .user import User

__all__ = ["Base", "Tenant", "User"]
```

Create `backend/src/models/base.py`:
```python
from sqlalchemy import Column, Integer, DateTime, String, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
import uuid

Base = declarative_base()

class TimestampMixin:
    """Mixin for adding timestamp fields to models"""
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

class UUIDMixin:
    """Mixin for adding UUID primary key"""
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))

class SoftDeleteMixin:
    """Mixin for soft delete functionality"""
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    def soft_delete(self):
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()

class TenantMixin:
    """Mixin for tenant isolation"""
    tenant_id = Column(String, nullable=False, index=True)
```

### 4.2 Create Tenant Model
Create `backend/src/models/tenant.py`:
```python
from sqlalchemy import Column, String, Boolean, JSON, Text
from sqlalchemy.orm import relationship
from .base import Base, TimestampMixin, UUIDMixin, SoftDeleteMixin

class Tenant(Base, UUIDMixin, TimestampMixin, SoftDeleteMixin):
    __tablename__ = "tenants"

    # Basic information
    name = Column(String(255), nullable=False)
    slug = Column(String(100), unique=True, nullable=False, index=True)
    domain = Column(String(255), unique=True, nullable=True)

    # Status
    is_active = Column(Boolean, default=True, nullable=False)

    # Configuration
    settings = Column(JSON, default=dict)

    # Branding
    logo_url = Column(String(500), nullable=True)
    primary_color = Column(String(7), default="#007bff")  # Hex color
    secondary_color = Column(String(7), default="#6c757d")  # Hex color

    # Subscription info (for future use)
    plan = Column(String(50), default="free")
    max_users = Column(Integer, default=100)

    # Contact information
    contact_email = Column(String(255), nullable=True)
    contact_phone = Column(String(50), nullable=True)

    # Address
    address = Column(Text, nullable=True)

    # Relationships
    users = relationship("User", back_populates="tenant", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Tenant(id={self.id}, name={self.name}, slug={self.slug})>"
```

## Step 5: PostgreSQL Row Level Security (RLS)

### 5.1 Create Database Initialization Script
Create `database/init/01-init-rls.sql`:
```sql
-- Enable Row Level Security extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create a function to get current tenant ID from session
CREATE OR REPLACE FUNCTION current_tenant_id() RETURNS TEXT AS $$
BEGIN
    RETURN current_setting('app.current_tenant_id', true);
END;
$$ LANGUAGE plpgsql STABLE;

-- Create a function to set tenant context
CREATE OR REPLACE FUNCTION set_tenant_context(tenant_id TEXT) RETURNS VOID AS $$
BEGIN
    PERFORM set_config('app.current_tenant_id', tenant_id, false);
END;
$$ LANGUAGE plpgsql;

-- Create tenant isolation policy function
CREATE OR REPLACE FUNCTION tenant_isolation_policy(tenant_id TEXT) RETURNS BOOLEAN AS $$
BEGIN
    RETURN tenant_id = current_tenant_id();
END;
$$ LANGUAGE plpgsql STABLE;
```

### 5.2 Create RLS Policies Setup
Create `database/migrations/001_setup_rls.sql`:
```sql
-- Create tenants table first (no RLS needed as it's the source of truth)
CREATE TABLE IF NOT EXISTS tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    domain VARCHAR(255) UNIQUE,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    settings JSONB DEFAULT '{}',
    logo_url VARCHAR(500),
    primary_color VARCHAR(7) DEFAULT '#007bff',
    secondary_color VARCHAR(7) DEFAULT '#6c757d',
    plan VARCHAR(50) DEFAULT 'free',
    max_users INTEGER DEFAULT 100,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    address TEXT,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_tenants_slug ON tenants(slug);
CREATE INDEX IF NOT EXISTS idx_tenants_domain ON tenants(domain);
CREATE INDEX IF NOT EXISTS idx_tenants_active ON tenants(is_active) WHERE is_active = TRUE;

-- Create users table with tenant_id for RLS
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    username VARCHAR(100),
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    hashed_password VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE NOT NULL,
    role VARCHAR(50) DEFAULT 'user' NOT NULL,
    last_login TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT FALSE NOT NULL,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(tenant_id, email),
    UNIQUE(tenant_id, username)
);

-- Create indexes for users
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(tenant_id, email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(tenant_id, username);
CREATE INDEX IF NOT EXISTS idx_users_active ON users(tenant_id, is_active) WHERE is_active = TRUE;

-- Enable RLS on users table
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for users
CREATE POLICY tenant_isolation_policy ON users
    FOR ALL
    TO PUBLIC
    USING (tenant_id::TEXT = current_tenant_id());

-- Create policy for tenant admins to see all users in their tenant
CREATE POLICY tenant_admin_policy ON users
    FOR ALL
    TO PUBLIC
    USING (
        tenant_id::TEXT = current_tenant_id() OR
        EXISTS (
            SELECT 1 FROM users u
            WHERE u.id::TEXT = current_setting('app.current_user_id', true)
            AND u.tenant_id = users.tenant_id
            AND u.role IN ('owner', 'admin')
        )
    );
```

### 5.3 Create Database Session Management
Create `backend/src/core/tenant_context.py`:
```python
from sqlalchemy.orm import Session
from sqlalchemy import text
from contextlib import contextmanager
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class TenantContext:
    """Manages tenant context for database operations"""

    def __init__(self, db: Session):
        self.db = db
        self._current_tenant_id: Optional[str] = None
        self._current_user_id: Optional[str] = None

    def set_tenant_context(self, tenant_id: str, user_id: Optional[str] = None):
        """Set the tenant context for RLS"""
        try:
            self.db.execute(text("SELECT set_config('app.current_tenant_id', :tenant_id, false)"),
                          {"tenant_id": tenant_id})
            self._current_tenant_id = tenant_id

            if user_id:
                self.db.execute(text("SELECT set_config('app.current_user_id', :user_id, false)"),
                              {"user_id": user_id})
                self._current_user_id = user_id

            logger.debug(f"Set tenant context: tenant_id={tenant_id}, user_id={user_id}")
        except Exception as e:
            logger.error(f"Failed to set tenant context: {e}")
            raise

    def clear_tenant_context(self):
        """Clear the tenant context"""
        try:
            self.db.execute(text("SELECT set_config('app.current_tenant_id', '', false)"))
            self.db.execute(text("SELECT set_config('app.current_user_id', '', false)"))
            self._current_tenant_id = None
            self._current_user_id = None
            logger.debug("Cleared tenant context")
        except Exception as e:
            logger.error(f"Failed to clear tenant context: {e}")
            raise

    @property
    def current_tenant_id(self) -> Optional[str]:
        return self._current_tenant_id

    @property
    def current_user_id(self) -> Optional[str]:
        return self._current_user_id

@contextmanager
def tenant_context(db: Session, tenant_id: str, user_id: Optional[str] = None):
    """Context manager for tenant operations"""
    context = TenantContext(db)
    try:
        context.set_tenant_context(tenant_id, user_id)
        yield context
    finally:
        context.clear_tenant_context()
```

## Step 6: Testing and Validation

### 6.1 Create Test Configuration
Create `backend/tests/conftest.py`:
```python
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
import os

from src.main import app
from src.core.database import get_db, Base
from src.models.tenant import Tenant
from src.models.user import User

# Test database URL
TEST_DATABASE_URL = os.getenv(
    "TEST_DATABASE_URL",
    "postgresql://postgres:postgres_dev_password@localhost:5432/arroyo_university_test"
)

@pytest.fixture(scope="session")
def test_engine():
    engine = create_engine(TEST_DATABASE_URL)
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def test_db(test_engine):
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.rollback()
        db.close()

@pytest.fixture(scope="function")
def client(test_db):
    def override_get_db():
        yield test_db

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()

@pytest.fixture
def sample_tenant(test_db):
    tenant = Tenant(
        name="Test University",
        slug="test-university",
        domain="test.example.com"
    )
    test_db.add(tenant)
    test_db.commit()
    test_db.refresh(tenant)
    return tenant
```

### 6.2 Create Infrastructure Tests
Create `backend/tests/test_infrastructure.py`:
```python
import pytest
from sqlalchemy import text
from src.core.database import test_db_connection
from src.core.tenant_context import tenant_context
from src.models.tenant import Tenant

def test_database_connection():
    """Test database connectivity"""
    assert test_db_connection() == True

def test_tenant_creation(test_db):
    """Test tenant model creation"""
    tenant = Tenant(
        name="Test Tenant",
        slug="test-tenant",
        domain="test.example.com"
    )
    test_db.add(tenant)
    test_db.commit()
    test_db.refresh(tenant)

    assert tenant.id is not None
    assert tenant.name == "Test Tenant"
    assert tenant.slug == "test-tenant"
    assert tenant.is_active == True

def test_tenant_context_management(test_db, sample_tenant):
    """Test tenant context setting and clearing"""
    with tenant_context(test_db, sample_tenant.id) as ctx:
        # Verify context is set
        result = test_db.execute(text("SELECT current_tenant_id()")).scalar()
        assert result == sample_tenant.id
        assert ctx.current_tenant_id == sample_tenant.id

    # Verify context is cleared after exiting
    result = test_db.execute(text("SELECT current_tenant_id()")).scalar()
    assert result == "" or result is None

def test_api_health_check(client):
    """Test API health endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data

def test_api_root(client):
    """Test API root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "ArroyoUniversity API"
    assert "version" in data
```

## Step 7: Deployment Scripts

### 7.1 Create Development Setup Script
Create `scripts/setup-dev.sh`:
```bash
#!/bin/bash

echo "Setting up ArroyoUniversity development environment..."

# Check prerequisites
command -v docker >/dev/null 2>&1 || { echo "Docker is required but not installed. Aborting." >&2; exit 1; }
command -v docker-compose >/dev/null 2>&1 || { echo "Docker Compose is required but not installed. Aborting." >&2; exit 1; }

# Create environment files
echo "Creating environment files..."
cat > .env << EOF
# Database
DATABASE_URL=postgresql://postgres:postgres_dev_password@localhost:5432/arroyo_university
TEST_DATABASE_URL=postgresql://postgres:postgres_dev_password@localhost:5432/arroyo_university_test

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-secret-key-change-in-production-$(openssl rand -hex 32)

# Environment
ENVIRONMENT=development

# API Keys (add your keys here)
OPENAI_API_KEY=your-openai-api-key-here

# Email (for later sprints)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
EOF

# Start services
echo "Starting Docker services..."
docker-compose up -d postgres redis

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL to be ready..."
until docker-compose exec postgres pg_isready -U postgres; do
  sleep 1
done

# Run database migrations
echo "Running database setup..."
docker-compose exec postgres psql -U postgres -d arroyo_university -f /docker-entrypoint-initdb.d/01-init-rls.sql

# Create test database
docker-compose exec postgres createdb -U postgres arroyo_university_test

echo "Development environment setup complete!"
echo "Run 'docker-compose up' to start all services"
```

### 7.2 Create Enhanced Makefile with Test Integration
Create `Makefile`:
```makefile
.PHONY: help setup dev dev-with-tests test clean build validate

# Configuration
VALIDATE_ON_START ?= false
QUICK_VALIDATE ?= true
VERBOSE_TESTS ?= false

help:
	@echo "Available commands:"
	@echo "  setup           - Set up development environment"
	@echo "  dev             - Start development servers"
	@echo "  dev-with-tests  - Start servers with automatic validation"
	@echo "  test            - Run full test suite"
	@echo "  test-quick      - Run quick validation tests"
	@echo "  validate        - Quick environment validation"
	@echo "  clean           - Clean up containers and volumes"
	@echo "  build           - Build all containers"
	@echo ""
	@echo "Environment variables:"
	@echo "  VALIDATE_ON_START=true  - Auto-validate on 'make dev'"
	@echo "  QUICK_VALIDATE=false   - Use full tests instead of quick validation"
	@echo "  VERBOSE_TESTS=true     - Show detailed test output"

setup:
	@chmod +x scripts/setup-dev.sh
	@chmod +x tests/validate_sprint1.sh
	@./scripts/setup-dev.sh

dev:
	@echo "Starting development environment..."
	@docker-compose up -d
	@echo "Waiting for services to be ready..."
	@sleep 10
ifeq ($(VALIDATE_ON_START),true)
	@echo "Running automatic validation..."
	@$(MAKE) validate-auto
endif
	@echo "✅ Development environment ready!"
	@echo "📊 API Documentation: http://localhost:8000/docs"
	@echo "🏥 Health Check: http://localhost:8000/health"
	@echo "🔧 Run 'make logs' to see service logs"
	@echo "🧪 Run 'make validate' to check system health"
	@docker-compose logs --tail=20

dev-with-tests:
	@echo "Starting development environment with validation..."
	@VALIDATE_ON_START=true $(MAKE) dev

dev-interactive:
	@echo "Starting development environment in interactive mode..."
	@docker-compose up

validate:
	@echo "🔍 Running quick environment validation..."
	@./tests/validate_sprint1.sh

validate-auto:
ifeq ($(QUICK_VALIDATE),true)
	@echo "🔍 Quick validation..."
	@./tests/validate_sprint1.sh || (echo "⚠️  Validation failed but continuing..." && true)
else
	@echo "🧪 Running comprehensive tests..."
	@python tests/run_sprint1_tests.py || (echo "⚠️  Tests failed but continuing..." && true)
endif

test:
	@echo "🧪 Running full test suite..."
ifeq ($(VERBOSE_TESTS),true)
	@python tests/run_sprint1_tests.py
else
	@python tests/run_sprint1_tests.py 2>/dev/null || (echo "❌ Tests failed. Run with VERBOSE_TESTS=true for details" && exit 1)
endif

test-quick:
	@echo "⚡ Running quick tests..."
	@pytest tests/sprint-1/test_infrastructure.py::TestAcceptanceCriteria -v

test-infrastructure:
	@echo "🏗️  Testing infrastructure..."
	@pytest tests/sprint-1/test_infrastructure.py -v

test-multi-tenant:
	@echo "🏢 Testing multi-tenant architecture..."
	@pytest tests/sprint-1/test_multi_tenant.py -v

test-database:
	@echo "🔒 Testing database security..."
	@pytest tests/sprint-1/test_database_rls.py -v

test-integration:
	@echo "🔗 Testing integration..."
	@pytest tests/integration/test_sprint1_integration.py -v

clean:
	@echo "🧹 Cleaning up..."
	@docker-compose down -v
	@docker system prune -f

build:
	@echo "🔨 Building containers..."
	@docker-compose build --no-cache

rebuild:
	@echo "🔄 Rebuilding and restarting..."
	@$(MAKE) clean
	@$(MAKE) build
	@$(MAKE) dev-with-tests

# Service-specific commands
dev-backend:
	@docker-compose up postgres redis backend

dev-frontend:
	@docker-compose up frontend

logs:
	@docker-compose logs -f

logs-backend:
	@docker-compose logs -f backend

logs-db:
	@docker-compose logs -f postgres

# Debugging commands
shell-backend:
	@docker-compose exec backend bash

shell-db:
	@docker-compose exec postgres psql -U postgres -d arroyo_university

debug-health:
	@echo "🏥 Checking service health..."
	@echo "Backend Health:"
	@curl -s http://localhost:8000/health | jq . || echo "❌ Backend not responding"
	@echo "\nDatabase Health:"
	@docker-compose exec -T postgres pg_isready -U postgres || echo "❌ Database not ready"
	@echo "\nRedis Health:"
	@docker-compose exec -T redis redis-cli ping || echo "❌ Redis not responding"

# Status and monitoring
status:
	@echo "📊 System Status:"
	@docker-compose ps
	@echo "\n🔍 Quick Health Check:"
	@$(MAKE) debug-health

# Development workflow shortcuts
fresh-start:
	@echo "🆕 Fresh start - cleaning and rebuilding everything..."
	@$(MAKE) clean
	@$(MAKE) setup
	@$(MAKE) dev-with-tests

quick-restart:
	@echo "⚡ Quick restart..."
	@docker-compose restart
	@sleep 5
	@$(MAKE) validate

# CI/CD helpers
ci-test:
	@echo "🤖 Running CI tests..."
	@VERBOSE_TESTS=true python tests/run_sprint1_tests.py

ci-validate:
	@echo "🤖 Running CI validation..."
	@./tests/validate_sprint1.sh
```

## Step 8: Validation Checklist

### 8.1 Infrastructure Validation
- [ ] Docker containers start successfully
- [ ] PostgreSQL database is accessible
- [ ] Redis is running and accessible
- [ ] Backend API responds to health checks
- [ ] Frontend development server starts

### 8.2 Multi-tenant Architecture Validation
- [ ] Tenant model creates successfully
- [ ] RLS policies are applied correctly
- [ ] Tenant context can be set and cleared
- [ ] Database queries respect tenant boundaries

### 8.3 Development Environment Validation
- [ ] Environment variables are loaded correctly
- [ ] Database migrations run successfully
- [ ] Tests pass
- [ ] API documentation is accessible at `/docs`

## Next Steps

After completing Sprint 1:
1. Verify all tests pass
2. Confirm Docker environment is stable
3. Test tenant isolation manually
4. Proceed to Sprint 2: User and Tenant Management

## Troubleshooting

### Common Issues
1. **Port conflicts**: Ensure ports 3000, 5432, 6379, 8000 are available
2. **Docker permissions**: Run Docker commands with appropriate permissions
3. **Database connection**: Verify PostgreSQL is running and accessible
4. **Environment variables**: Check .env file is properly loaded

### Performance Considerations
- Database connection pooling is configured
- Indexes are created for frequently queried fields
- RLS policies are optimized for performance
- Caching strategy is prepared for implementation
```
```
```
