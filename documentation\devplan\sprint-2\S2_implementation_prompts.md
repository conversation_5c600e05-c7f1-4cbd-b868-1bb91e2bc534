# Sprint 2 Implementation Prompts - AI-Assisted Development Guide

## 📋 Overview

This document provides specific AI prompts to help implement each task in Sprint 2 user stories. Use these prompts with Augment Code or other AI assistants to accelerate development while maintaining quality and consistency.

## 🔐 US1: JWT Authentication System (HU-2.1)

### Task 1.1: Create User Data Model
```
Create a SQLAlchemy User model for a multi-tenant platform with the following requirements:
- Inherit from Base, UUIDMixin, TimestampMixin, SoftDeleteMixin, TenantMixin
- Include authentication fields: email (unique within tenant), hashed_password, role
- Include profile fields: first_name, last_name, username, avatar_url, bio
- Include tracking fields: last_login, login_count, is_active, is_verified
- Include tenant relationship and helper properties (full_name, is_owner, is_admin)
- Add proper indexes for email and role fields
- Support roles: "owner", "admin", "user"
- Include timezone and language preferences
```

### Task 1.2: Implement Security Utilities
```
Create a comprehensive security module with the following functions:
- Password hashing using bcrypt with 12+ rounds
- JWT token creation with user_id, tenant_id, role, email claims
- JWT token validation with proper error handling
- Configurable token expiration (default 24 hours)
- Secure random token generation for password resets
- Use python-jose for JWT handling and passlib for password hashing
- Include proper type hints and error handling
```

### Task 1.3: Create Authentication Dependencies
```
Create FastAPI dependencies for authentication with the following features:
- get_current_user: Extract and validate JWT token, set tenant context for RLS
- get_current_active_user: Ensure user is active
- get_current_admin_user: Ensure user has admin or owner role
- get_current_tenant: Get user's tenant information
- Proper HTTP 401/403 error responses
- Integration with tenant context for row-level security
- Use HTTPBearer for token extraction
```

### Task 1.4: Define Authentication Schemas
```
Create Pydantic schemas for authentication with these requirements:
- UserCreate: email, password (min 8 chars), optional profile fields, tenant_id
- UserUpdate: optional profile fields, role assignment
- LoginRequest: email, password, optional tenant_slug
- Token: access_token, token_type, expires_in, user object
- Password reset schemas with secure token handling
- Proper validation for email format and password strength
```

### Task 1.5: Implement Authentication Service
```
Create an AuthService class with these methods:
- authenticate_user: validate email/password, support tenant-specific and owner login
- create_access_token_for_user: generate JWT with proper claims
- create_user: create new user with email uniqueness validation within tenant
- Update login tracking (last_login, login_count) on successful authentication
- Proper error handling for invalid credentials and duplicate emails
- Integration with tenant context for multi-tenancy
```

### Task 1.6: Create Authentication API Endpoints
```
Create FastAPI authentication endpoints:
- POST /auth/login: accept LoginRequest, return Token
- POST /auth/register: accept UserCreate, return UserInDB
- POST /auth/token: OAuth2-compatible endpoint for token generation
- Proper HTTP status codes (200, 401, 400, 422)
- Comprehensive error handling and validation
- OpenAPI documentation with examples
- Rate limiting considerations
```

## 🏢 US2: Tenant CRUD Operations (HU-2.2)

### Task 2.1: Create Tenant Schemas
```
Create Pydantic schemas for tenant management:
- TenantCreate: name, slug (URL-safe validation), domain, owner user fields
- TenantUpdate: partial updates for settings, branding (logo_url, colors)
- TenantInDB: complete tenant info with statistics
- TenantWithStats: includes user_count, active_user_count
- Validation for slug uniqueness and domain format
- Support for JSONB settings field for flexible configuration
```

### Task 2.2: Implement Tenant Service
```
Create a TenantService class with these methods:
- create_tenant: validate slug/domain uniqueness, create tenant + owner user in transaction
- get_tenant_by_id and get_tenant_by_slug: with soft deletion support
- get_tenants_with_stats: include user count aggregation with SQL joins
- update_tenant: partial updates with validation
- delete_tenant: soft deletion implementation
- Proper error handling for conflicts and not found scenarios
```

### Task 2.3: Create Tenant API Endpoints
```
Create tenant management endpoints:
- GET /tenants/current: return current user's tenant info
- PUT /tenants/current: update current tenant (admin only)
- GET /tenants: list all tenants with stats (system owner only)
- POST /tenants: create new tenant (system owner only)
- GET /tenants/{id}: get specific tenant (system owner only)
- DELETE /tenants/{id}: soft delete tenant (system owner only)
- Proper role-based access control and error handling
```

## 👥 US3: User Management System (HU-2.3)

### Task 3.1: Create User Management Schemas
```
Create comprehensive user management schemas:
- UserSearch: filtering by role, status, activity with pagination
- BulkUserCreate: for CSV import functionality
- UserActivity: for tracking user actions and login history
- UserProfile: detailed user information with privacy controls
- Validation for bulk operations and search parameters
- Support for partial updates and role assignment
```

### Task 3.2: Implement User Service
```
Create a UserService class with these methods:
- create_user: with email uniqueness validation within tenant
- update_user: partial updates with role validation
- delete_user: soft deletion with audit trail
- search_users: with filtering, pagination, and sorting
- bulk_create_users: handle CSV import with error reporting
- bulk_update_users: mass operations with transaction safety
- get_user_activity: activity logs with date range filtering
```

### Task 3.3: Create User Management API Endpoints
```
Create user management endpoints:
- GET /users: list with search, filter, pagination
- POST /users: create individual user
- GET /users/{id}: get user details
- PUT /users/{id}: update user
- DELETE /users/{id}: soft delete user
- POST /users/bulk: bulk user creation
- PUT /users/bulk: bulk user updates
- GET /users/{id}/activity: user activity logs
- Enforce tenant isolation and role-based permissions
```

## 🔒 US4: Role-Based Access Control (HU-2.4)

### Task 4.1: Define Permission System Architecture
```
Create a comprehensive RBAC system:
- Define permission constants for all system operations
- Implement three-tier role hierarchy: Owner > Admin > User
- Create permission checking utilities and decorators
- Design extensible system for future custom permissions
- Document permission inheritance rules
- Create role-based permission matrix
```

### Task 4.2: Implement Authorization Middleware
```
Create authorization middleware with these features:
- @require_permission decorator for endpoint protection
- @require_role decorator for role-based access
- Permission validation on each request with caching
- Request context with user role and permissions
- Efficient permission checking (<10ms overhead)
- Proper HTTP 403 responses for unauthorized access
```

### Task 4.3: Update Authentication Dependencies
```
Enhance authentication dependencies with RBAC:
- get_current_owner_user: system owner access only
- require_permission dependency factory
- Tenant-aware permission checking
- Role validation with inheritance rules
- Clear error messages for permission failures
- Performance optimization for frequent checks
```

## 🎛️ US5: Admin Interface (HU-2.5)

### Task 5.1: Create Admin Dashboard Layout
```
Create a React admin dashboard with:
- Responsive layout with sidebar navigation
- Overview cards showing key metrics (user count, activity)
- Role-based navigation menu (owner sees more options)
- Modern UI with loading states and error handling
- Mobile-responsive design
- Integration with authentication context
```

### Task 5.2: Implement User Management Interface
```
Create user management interface with:
- User list with search, filtering, and pagination
- Create/edit user modals with form validation
- Bulk operations (activate, deactivate, delete)
- User activity history view
- Role assignment interface
- Export functionality for user data
- Real-time updates for user changes
```

### Task 5.3: Create Tenant Settings Interface
```
Create tenant customization interface with:
- Tenant branding (logo upload, color picker)
- Organization settings (name, contact info)
- Notification preferences configuration
- Settings preview before saving
- Form validation and error handling
- Save confirmation and success feedback
```

## 📧 US6: Invitation System (HU-2.6)

### Task 6.1: Create Invitation Data Model
```
Create an Invitation SQLAlchemy model with:
- Inherit from Base, UUIDMixin, TimestampMixin, TenantMixin
- Fields: email, token (32-byte secure), role, expires_at
- Status tracking: is_used, used_at, invited_by
- Helper properties: is_expired, is_valid
- Relationship with Tenant model
- Proper indexes for token and email fields
```

### Task 6.2: Create Invitation Schemas
```
Create invitation Pydantic schemas:
- InvitationCreate: email, role, optional user info and message
- InvitationAccept: token, password, optional user details
- InvitationInDB: complete invitation info for API responses
- Validation for email format and password strength
- Role validation against allowed roles
```

### Task 6.3: Implement Invitation Service
```
Create InvitationService with these methods:
- create_invitation: validate email uniqueness, generate secure token
- accept_invitation: create user account, mark invitation as used
- get_invitation_by_token: with validation
- get_tenant_invitations: list with filtering
- cancel_invitation: with proper validation
- resend_invitation: with rate limiting
- Integration with email service for sending invitations
```

### Task 6.4: Create Email Templates
```
Create professional email templates:
- HTML template with tenant branding (logo, colors)
- Plain text fallback template
- Template variables for personalization
- Responsive email design
- Clear invitation acceptance instructions
- Secure invitation link with token
- Template rendering system with Jinja2
```

## 🧪 Testing Prompts

### Unit Test Generation
```
Generate comprehensive unit tests for [SERVICE_NAME] with:
- Test all public methods with various scenarios
- Mock external dependencies (database, email service)
- Test error handling and edge cases
- Use pytest fixtures for test data
- Achieve >95% code coverage
- Include performance tests for critical paths
```

### Integration Test Creation
```
Create integration tests for [ENDPOINT_GROUP] with:
- Test complete API workflows
- Test authentication and authorization
- Test tenant isolation
- Test error responses and status codes
- Use TestClient for FastAPI testing
- Include database transaction testing
```

## 🔧 Debugging Prompts

### Authentication Issues
```
Debug authentication problems in my JWT implementation:
- Token validation is failing with error: [ERROR_MESSAGE]
- Check token structure, claims, and expiration
- Verify secret key configuration
- Test password hashing and verification
- Validate middleware integration
```

### Database Issues
```
Debug database/RLS issues in my multi-tenant setup:
- Tenant isolation is not working properly
- Users can see data from other tenants
- Check RLS policies and tenant context setting
- Validate foreign key relationships
- Test query performance with explain plans
```

### Permission Issues
```
Debug RBAC permission problems:
- User with role [ROLE] cannot access [ENDPOINT]
- Permission checking is too slow
- Role inheritance is not working correctly
- Check permission decorators and middleware
- Validate role assignment logic
```

## 📊 Performance Optimization Prompts

### Database Optimization
```
Optimize database queries for [FEATURE]:
- Add appropriate indexes for frequent queries
- Optimize N+1 query problems
- Implement query result caching
- Use database connection pooling
- Profile slow queries and optimize
```

### API Performance
```
Optimize API performance for [ENDPOINT]:
- Reduce response time to under [TARGET]ms
- Implement response caching where appropriate
- Optimize serialization and validation
- Add pagination for large datasets
- Profile and optimize bottlenecks
```

## 🔄 Code Review and Refactoring Prompts

### Code Review Request
```
Review my implementation of [COMPONENT] for:
- Code quality and best practices
- Security vulnerabilities
- Performance issues
- Error handling completeness
- Type safety and documentation
- Adherence to project patterns
- Test coverage adequacy

Code to review:
[PASTE YOUR CODE HERE]
```

### Refactoring Guidance
```
Refactor this [COMPONENT] to improve:
- Maintainability and readability
- Performance and efficiency
- Error handling and resilience
- Testability and modularity
- Security and validation
- Follow SOLID principles

Current implementation:
[PASTE YOUR CODE HERE]

Specific concerns: [LIST YOUR CONCERNS]
```

## 🐛 Error Handling and Validation Prompts

### Comprehensive Error Handling
```
Add comprehensive error handling to my [SERVICE/ENDPOINT] with:
- Specific exception types for different error scenarios
- Proper HTTP status codes for API endpoints
- User-friendly error messages
- Logging for debugging and monitoring
- Graceful degradation where possible
- Input validation and sanitization
```

### Validation Implementation
```
Implement robust validation for [DATA_MODEL/SCHEMA] with:
- Field-level validation rules
- Cross-field validation logic
- Custom validators for business rules
- Proper error messages for users
- Performance-optimized validation
- Security considerations (SQL injection, XSS prevention)
```

## 🔐 Security Implementation Prompts

### Security Hardening
```
Harden the security of my [COMPONENT] by implementing:
- Input sanitization and validation
- SQL injection prevention
- XSS protection
- CSRF protection
- Rate limiting
- Secure headers
- Authentication bypass prevention
- Authorization checks
```

### Security Audit
```
Perform a security audit of my [FEATURE] implementation:
- Check for common vulnerabilities (OWASP Top 10)
- Validate authentication and authorization
- Review data access patterns
- Check for information disclosure
- Validate input handling
- Review error messages for information leakage
```

## 📈 Monitoring and Logging Prompts

### Logging Implementation
```
Add comprehensive logging to my [COMPONENT] with:
- Structured logging with consistent format
- Appropriate log levels (DEBUG, INFO, WARN, ERROR)
- Security event logging
- Performance metrics logging
- Error tracking and alerting
- Log correlation IDs for tracing
- Sensitive data protection in logs
```

### Monitoring Setup
```
Set up monitoring for [FEATURE] with:
- Health check endpoints
- Performance metrics collection
- Error rate monitoring
- User activity tracking
- Resource usage monitoring
- Alert thresholds and notifications
- Dashboard creation for key metrics
```

## 🧪 Advanced Testing Prompts

### Load Testing
```
Create load tests for [ENDPOINT/FEATURE] with:
- Simulate [NUMBER] concurrent users
- Test various usage patterns
- Measure response times and throughput
- Identify performance bottlenecks
- Test system behavior under stress
- Validate auto-scaling if applicable
```

### Security Testing
```
Create security tests for [FEATURE] with:
- Authentication bypass attempts
- Authorization escalation tests
- Input validation boundary tests
- SQL injection and XSS tests
- Rate limiting validation
- Session management tests
- Data access control tests
```

### End-to-End Testing
```
Create E2E tests for [USER_WORKFLOW] with:
- Complete user journey simulation
- Cross-browser compatibility
- Mobile responsiveness testing
- Error scenario handling
- Performance validation
- Accessibility compliance
- Integration with CI/CD pipeline
```

## 🚀 Deployment and DevOps Prompts

### Docker Configuration
```
Create Docker configuration for [SERVICE] with:
- Multi-stage build for optimization
- Security best practices
- Environment variable management
- Health checks
- Resource limits
- Non-root user execution
- Minimal base image
```

### CI/CD Pipeline
```
Set up CI/CD pipeline for Sprint 2 with:
- Automated testing on pull requests
- Code quality checks and linting
- Security scanning
- Database migration testing
- Deployment automation
- Rollback procedures
- Environment promotion strategy
```

### Environment Configuration
```
Configure environments for [ENVIRONMENT] with:
- Environment-specific variables
- Database connection settings
- Email service configuration
- Security settings (JWT secrets, etc.)
- Monitoring and logging setup
- Performance optimization settings
- Backup and recovery procedures
```

## 📚 Documentation Prompts

### API Documentation
```
Generate comprehensive API documentation for [ENDPOINTS] with:
- OpenAPI/Swagger specifications
- Request/response examples
- Authentication requirements
- Error response documentation
- Rate limiting information
- Usage examples and tutorials
- SDK/client library examples
```

### Code Documentation
```
Add comprehensive documentation to [CODE_COMPONENT] with:
- Docstrings for all functions and classes
- Type hints for all parameters and returns
- Usage examples and edge cases
- Performance considerations
- Security notes
- Integration guidelines
- Troubleshooting guide
```

## 🎯 Task-Specific Implementation Prompts

### Database Migration
```
Create database migration for [CHANGES] with:
- Forward migration script
- Rollback migration script
- Data preservation during schema changes
- Index creation/modification
- Constraint updates
- Performance impact assessment
- Testing on staging environment
```

### Frontend Component
```
Create React component for [FEATURE] with:
- TypeScript interfaces and props
- Responsive design implementation
- Accessibility compliance (ARIA labels, keyboard navigation)
- Error boundary handling
- Loading and error states
- Integration with API services
- Unit tests with React Testing Library
```

### API Integration
```
Integrate with [EXTERNAL_SERVICE] API with:
- Authentication handling
- Rate limiting compliance
- Error handling and retries
- Response caching where appropriate
- Webhook handling if applicable
- Data transformation and validation
- Monitoring and alerting
```

## 🔧 Troubleshooting Prompts

### Performance Issues
```
Diagnose and fix performance issues in [COMPONENT]:
- Current performance: [CURRENT_METRICS]
- Target performance: [TARGET_METRICS]
- Identify bottlenecks using profiling
- Optimize database queries
- Implement caching strategies
- Review algorithm complexity
- Consider architectural changes
```

### Memory Issues
```
Diagnose and fix memory issues in [APPLICATION]:
- Memory usage patterns
- Potential memory leaks
- Garbage collection optimization
- Connection pool management
- Cache size optimization
- Resource cleanup procedures
```

### Concurrency Issues
```
Diagnose and fix concurrency issues in [FEATURE]:
- Race condition identification
- Deadlock prevention
- Transaction isolation levels
- Lock contention analysis
- Async/await optimization
- Thread safety validation
```

## 📋 Daily Development Workflow Prompts

### Morning Planning
```
Plan today's development tasks:
- Review Sprint 2 progress
- Identify dependencies for today's tasks
- Set up development environment
- Review code changes from team
- Plan testing approach
- Estimate completion times
```

### End-of-Day Review
```
Review today's development progress:
- Completed tasks and quality check
- Identify any blockers or issues
- Update task status and documentation
- Plan tomorrow's priorities
- Commit and push code changes
- Update team on progress
```

Use these prompts as starting points and customize them based on your specific implementation needs, current context, and the particular challenges you're facing during Sprint 2 development.
