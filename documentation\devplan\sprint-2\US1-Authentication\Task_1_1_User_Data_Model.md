# Task 1.1: Create User Data Model

## 📋 Task Overview
**User Story:** HU-2.1 - JWT Authentication System  
**Task ID:** 1.1  
**Estimated Time:** 2 hours  
**Priority:** Critical  
**Complexity:** Medium  

## 🎯 Description
Implement the User model with all authentication and profile fields required for the multi-tenant platform. This model serves as the foundation for all user-related operations and authentication.

## 📦 Deliverables
- [ ] Create `backend/src/models/user.py`
- [ ] User model with authentication fields (email, hashed_password, role)
- [ ] Profile fields (first_name, last_name, avatar_url, bio)
- [ ] Login tracking fields (last_login, login_count)
- [ ] Tenant relationship and role properties
- [ ] Proper indexes for performance

## ✅ Acceptance Criteria
- [ ] User model inherits from Base, UUIDMixin, TimestampMixin, SoftDeleteMixin, TenantMixin
- [ ] Email field is unique within tenant and indexed
- [ ] Password is stored as hashed_password (never plain text)
- [ ] Role field supports "owner", "admin", "user" values
- [ ] Login tracking fields are properly typed
- [ ] Tenant relationship is established
- [ ] Helper properties (full_name, is_owner, is_admin) work correctly

## 🔧 Technical Requirements

### Model Structure
```python
class User(Base, UUIDMixin, TimestampMixin, SoftDeleteMixin, TenantMixin):
    __tablename__ = "users"
    
    # Authentication fields
    email = Column(String(255), nullable=False, index=True)
    hashed_password = Column(String(255), nullable=False)
    role = Column(String(50), default="user", nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Profile fields
    username = Column(String(100), nullable=True, index=True)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    bio = Column(String(500), nullable=True)
    
    # Preferences
    timezone = Column(String(50), default="UTC")
    language = Column(String(10), default="en")
    
    # Login tracking
    last_login = Column(DateTime(timezone=True), nullable=True)
    login_count = Column(Integer, default=0)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="users")
```

### Required Indexes
- Email field (for login performance)
- Role field (for permission queries)
- Tenant_id + email (for uniqueness within tenant)
- Username field (for search functionality)

### Helper Properties
```python
@property
def full_name(self):
    if self.first_name and self.last_name:
        return f"{self.first_name} {self.last_name}"
    return self.first_name or self.last_name or self.username or self.email

@property
def is_owner(self):
    return self.role == "owner"

@property
def is_admin(self):
    return self.role in ["owner", "admin"]
```

## 🔗 Dependencies
- **Prerequisite:** Base model classes (UUIDMixin, TimestampMixin, etc.) must exist
- **Database:** PostgreSQL with proper RLS policies
- **Models:** Tenant model must exist for relationship

## 🧪 Testing Requirements
- [ ] Test model creation with all fields
- [ ] Test helper properties functionality
- [ ] Test relationship with Tenant model
- [ ] Test field validation and constraints
- [ ] Test database indexes are created
- [ ] Test email uniqueness within tenant

## 📊 Validation Checklist
- [ ] Model follows project naming conventions
- [ ] All required fields are properly typed
- [ ] Relationships are correctly defined
- [ ] Indexes are optimized for query patterns
- [ ] Helper methods work as expected
- [ ] Model integrates with existing base classes

## 🚨 Security Considerations
- [ ] Password field is named hashed_password (never store plain text)
- [ ] Email field is properly indexed for performance
- [ ] Role field has proper validation
- [ ] Tenant isolation is maintained through TenantMixin

## 📈 Performance Considerations
- [ ] Email index for fast login queries
- [ ] Role index for permission checks
- [ ] Composite index on tenant_id + email for uniqueness
- [ ] Avoid N+1 queries in relationships

## 🔄 Implementation Steps
1. **Create the model file** - `backend/src/models/user.py`
2. **Define the User class** with all required fields
3. **Add helper properties** for convenience methods
4. **Define relationships** with Tenant model
5. **Add proper indexes** for performance
6. **Test the model** with sample data
7. **Validate constraints** and uniqueness rules

## 📝 Code Example
```python
from sqlalchemy import Column, String, Boolean, DateTime, Integer
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from .base import Base, TimestampMixin, UUIDMixin, SoftDeleteMixin, TenantMixin

class User(Base, UUIDMixin, TimestampMixin, SoftDeleteMixin, TenantMixin):
    __tablename__ = "users"
    
    # Basic information
    email = Column(String(255), nullable=False, index=True)
    username = Column(String(100), nullable=True, index=True)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    
    # Authentication
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Role and permissions
    role = Column(String(50), default="user", nullable=False)  # owner, admin, user
    
    # Login tracking
    last_login = Column(DateTime(timezone=True), nullable=True)
    login_count = Column(Integer, default=0)
    
    # Profile
    avatar_url = Column(String(500), nullable=True)
    bio = Column(String(500), nullable=True)
    timezone = Column(String(50), default="UTC")
    language = Column(String(10), default="en")
    
    # Relationships
    tenant = relationship("Tenant", back_populates="users")
    
    @property
    def full_name(self):
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.first_name or self.last_name or self.username or self.email
    
    @property
    def is_owner(self):
        return self.role == "owner"
    
    @property
    def is_admin(self):
        return self.role in ["owner", "admin"]
    
    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, role={self.role})>"
```

## 🎯 Definition of Done
- [ ] User model is created and follows all requirements
- [ ] All fields are properly typed and validated
- [ ] Helper properties work correctly
- [ ] Relationships are established
- [ ] Database indexes are created
- [ ] Model passes all tests
- [ ] Code review is completed
- [ ] Documentation is updated
