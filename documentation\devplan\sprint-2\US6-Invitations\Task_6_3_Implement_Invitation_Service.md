# Task 6.3: Implement Invitation Service

## 📋 Task Overview
**User Story:** HU-2.6 - Invitation System  
**Task ID:** 6.3  
**Estimated Time:** 3 hours  
**Priority:** High  
**Complexity:** High  

## 🎯 Description
Create business logic for invitation management that handles invitation creation with validation, invitation acceptance workflow, invitation management operations, and email integration for sending invitations.

## 📦 Deliverables
- [ ] Create `backend/src/services/invitation_service.py`
- [ ] Invitation creation with validation
- [ ] Invitation acceptance workflow
- [ ] Invitation management operations
- [ ] Email integration for sending invitations

## ✅ Acceptance Criteria
- [ ] create_invitation validates email uniqueness
- [ ] Prevents duplicate invitations for same email
- [ ] Generates cryptographically secure tokens
- [ ] accept_invitation creates user account
- [ ] Marks invitation as used after acceptance
- [ ] get_tenant_invitations with filtering
- [ ] cancel_invitation functionality
- [ ] resend_invitation capability
- [ ] Proper error handling for all operations

## 🔧 Technical Requirements

### Core Service Methods
1. **create_invitation(invitation_data: InvitationCreate) -> Invitation**
   - Validate email uniqueness within tenant
   - Generate secure token
   - Set expiration time
   - Send invitation email

2. **accept_invitation(token: str, user_data: UserCreate) -> User**
   - Validate invitation token
   - Create user account
   - Mark invitation as accepted
   - Handle transaction safety

3. **get_invitation_by_token(token: str) -> Optional[Invitation]**
   - Retrieve invitation with validation
   - Check expiration and status

4. **get_tenant_invitations(tenant_id: str, filters) -> List[Invitation]**
   - List invitations with filtering
   - Support pagination and sorting

5. **cancel_invitation(invitation_id: str) -> bool**
   - Cancel pending invitation
   - Validate permissions

6. **resend_invitation(invitation_id: str) -> bool**
   - Regenerate token if needed
   - Send new invitation email
   - Rate limiting

## 🔗 Dependencies
- **Prerequisite:** Invitation model (Task 6.1), Invitation schemas (Task 6.2)
- **Services:** AuthService for user creation, EmailService for sending
- **Models:** Invitation, User, Tenant models

## 📝 Code Example
```python
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from fastapi import HTTPException, status
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta

from ..models.invitation import Invitation, InvitationStatus
from ..models.user import User
from ..models.tenant import Tenant
from ..schemas.invitation import InvitationCreate, InvitationAccept
from ..schemas.auth import UserCreate
from ..services.auth_service import AuthService
from ..services.email_service import EmailService
from ..core.tenant_context import tenant_context

class InvitationService:
    def __init__(self, db: Session):
        self.db = db
        self.auth_service = AuthService(db)
        self.email_service = EmailService()

    def create_invitation(
        self,
        invitation_data: InvitationCreate,
        tenant_id: str,
        invited_by_user_id: str
    ) -> Invitation:
        """Create a new invitation"""
        with tenant_context(self.db, tenant_id):
            # Check if user already exists
            existing_user = self.db.query(User).filter(
                User.email == invitation_data.email,
                User.is_deleted == False
            ).first()

            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User with this email already exists"
                )

            # Check for existing pending invitation
            existing_invitation = self.db.query(Invitation).filter(
                Invitation.email == invitation_data.email,
                Invitation.status == InvitationStatus.PENDING,
                Invitation.tenant_id == tenant_id
            ).first()

            if existing_invitation:
                if not existing_invitation.is_expired:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="Pending invitation already exists for this email"
                    )
                else:
                    # Cancel expired invitation
                    existing_invitation.status = InvitationStatus.EXPIRED

            # Create new invitation
            invitation = Invitation(
                email=invitation_data.email,
                role=invitation_data.role,
                tenant_id=tenant_id,
                invited_by=invited_by_user_id,
                message=invitation_data.message,
                metadata=invitation_data.metadata
            )

            self.db.add(invitation)
            self.db.commit()
            self.db.refresh(invitation)

            # Send invitation email
            try:
                self._send_invitation_email(invitation)
            except Exception as e:
                # Log error but don't fail the invitation creation
                print(f"Failed to send invitation email: {e}")

            return invitation

    def accept_invitation(self, token: str, user_data: InvitationAccept) -> User:
        """Accept an invitation and create user account"""
        invitation = self.get_invitation_by_token(token)
        
        if not invitation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invitation not found"
            )

        if not invitation.is_valid:
            if invitation.is_expired:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invitation has expired"
                )
            elif invitation.is_accepted:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invitation has already been accepted"
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invitation is not valid"
                )

        try:
            # Create user account
            user_create_data = UserCreate(
                email=invitation.email,
                password=user_data.password,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                role=invitation.role,
                tenant_id=invitation.tenant_id
            )

            user = self.auth_service.create_user(user_create_data)

            # Mark invitation as accepted
            invitation.accept(user.id)
            self.db.commit()

            return user

        except Exception as e:
            self.db.rollback()
            raise e

    def get_invitation_by_token(self, token: str) -> Optional[Invitation]:
        """Get invitation by token"""
        return self.db.query(Invitation).filter(
            Invitation.token == token
        ).first()

    def get_tenant_invitations(
        self,
        tenant_id: str,
        status: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """Get invitations for a tenant with filtering"""
        with tenant_context(self.db, tenant_id):
            query = self.db.query(Invitation).filter(
                Invitation.tenant_id == tenant_id
            )

            if status:
                query = query.filter(Invitation.status == status)

            # Get total count
            total_count = query.count()

            # Apply pagination and ordering
            invitations = query.order_by(
                Invitation.created_at.desc()
            ).offset(skip).limit(limit).all()

            return {
                "invitations": invitations,
                "total_count": total_count,
                "page": skip // limit + 1,
                "pages": (total_count + limit - 1) // limit
            }

    def cancel_invitation(self, invitation_id: str, tenant_id: str) -> bool:
        """Cancel an invitation"""
        with tenant_context(self.db, tenant_id):
            invitation = self.db.query(Invitation).filter(
                Invitation.id == invitation_id,
                Invitation.tenant_id == tenant_id
            ).first()

            if not invitation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Invitation not found"
                )

            if not invitation.cancel():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Cannot cancel this invitation"
                )

            self.db.commit()
            return True

    def resend_invitation(self, invitation_id: str, tenant_id: str) -> bool:
        """Resend an invitation"""
        with tenant_context(self.db, tenant_id):
            invitation = self.db.query(Invitation).filter(
                Invitation.id == invitation_id,
                Invitation.tenant_id == tenant_id
            ).first()

            if not invitation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Invitation not found"
                )

            if invitation.status != InvitationStatus.PENDING:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Can only resend pending invitations"
                )

            # Extend expiration if needed
            if invitation.is_expired:
                invitation.extend_expiration()

            # Regenerate token for security
            invitation.regenerate_token()
            self.db.commit()

            # Send new invitation email
            try:
                self._send_invitation_email(invitation)
                return True
            except Exception as e:
                print(f"Failed to resend invitation email: {e}")
                return False

    def cleanup_expired_invitations(self) -> int:
        """Clean up expired invitations"""
        return Invitation.cleanup_expired(self.db)

    def get_invitation_statistics(self, tenant_id: str) -> Dict[str, Any]:
        """Get invitation statistics for a tenant"""
        with tenant_context(self.db, tenant_id):
            stats = {}
            
            # Total invitations
            stats["total"] = self.db.query(Invitation).filter(
                Invitation.tenant_id == tenant_id
            ).count()

            # By status
            for status in [InvitationStatus.PENDING, InvitationStatus.ACCEPTED, 
                          InvitationStatus.EXPIRED, InvitationStatus.CANCELLED]:
                stats[status] = self.db.query(Invitation).filter(
                    Invitation.tenant_id == tenant_id,
                    Invitation.status == status
                ).count()

            # Recent invitations (last 30 days)
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            stats["recent"] = self.db.query(Invitation).filter(
                Invitation.tenant_id == tenant_id,
                Invitation.created_at >= thirty_days_ago
            ).count()

            return stats

    def _send_invitation_email(self, invitation: Invitation) -> None:
        """Send invitation email"""
        # Get tenant information for branding
        tenant = self.db.query(Tenant).filter(
            Tenant.id == invitation.tenant_id
        ).first()

        # Get inviter information
        inviter = self.db.query(User).filter(
            User.id == invitation.invited_by
        ).first()

        # Prepare email data
        email_data = {
            "to_email": invitation.email,
            "invitation_token": invitation.token,
            "tenant_name": tenant.name if tenant else "Organization",
            "tenant_slug": tenant.slug if tenant else "",
            "inviter_name": inviter.full_name if inviter else "Team",
            "role": invitation.role,
            "message": invitation.message,
            "expires_at": invitation.expires_at,
            "tenant_branding": {
                "logo_url": tenant.logo_url if tenant else None,
                "primary_color": tenant.primary_color if tenant else "#007bff",
                "secondary_color": tenant.secondary_color if tenant else "#6c757d"
            }
        }

        # Send email
        self.email_service.send_invitation_email(email_data)

    def validate_invitation_token(self, token: str) -> Dict[str, Any]:
        """Validate invitation token and return invitation info"""
        invitation = self.get_invitation_by_token(token)
        
        if not invitation:
            return {"valid": False, "error": "Invitation not found"}

        if invitation.is_expired:
            return {"valid": False, "error": "Invitation has expired"}

        if invitation.status != InvitationStatus.PENDING:
            return {"valid": False, "error": "Invitation is no longer valid"}

        # Get tenant info for display
        tenant = self.db.query(Tenant).filter(
            Tenant.id == invitation.tenant_id
        ).first()

        return {
            "valid": True,
            "invitation": {
                "email": invitation.email,
                "role": invitation.role,
                "tenant_name": tenant.name if tenant else "Organization",
                "expires_at": invitation.expires_at,
                "message": invitation.message
            }
        }
```

## 🎯 Definition of Done
- [ ] InvitationService class is fully implemented
- [ ] Invitation creation validates email uniqueness
- [ ] Invitation acceptance creates user accounts
- [ ] Token validation works correctly
- [ ] Email integration is functional
- [ ] Invitation management operations work
- [ ] Error handling is comprehensive
- [ ] All tests pass with >95% coverage
- [ ] Performance requirements are met
- [ ] Code review is completed
