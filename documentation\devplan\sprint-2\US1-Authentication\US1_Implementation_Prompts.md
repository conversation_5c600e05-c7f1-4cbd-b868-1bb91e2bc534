# US1 Authentication System - Implementation Prompts

## 🎯 Overview
This guide provides effective prompts for implementing each task in the US1 Authentication system. Each prompt specifies the exact files to create/modify and includes context from related files.

---

## 📋 Task 1.1: Create User Data Model

### 🎯 Effective Prompt
```
Create a comprehensive User data model for a multi-tenant platform with JWT authentication.

**File to Create:** `backend/src/models/user.py`

**Requirements:**
- Inherit from Base, UUIDMixin, TimestampMixin, SoftDeleteMixin, TenantMixin
- Authentication fields: email, hashed_password, role, is_active, is_verified
- Profile fields: username, first_name, last_name, avatar_url, bio
- Login tracking: last_login, login_count
- Tenant relationship with foreign key
- Helper properties: full_name, is_owner, is_admin
- Proper indexes for email (unique within tenant), role, and tenant_id
- SQLAlchemy model with proper constraints and relationships

**Reference Files:**
- Check `backend/src/models/base.py` for mixin classes
- Check `backend/src/models/tenant.py` for tenant relationship structure
- Follow existing model patterns in the codebase

**Include:** Complete SQLAlchemy model with all fields, relationships, properties, and database constraints.
```

---

## 📋 Task 1.2: Implement Security Utilities

### 🎯 Effective Prompt
```
Create comprehensive security utilities for JWT authentication and password hashing.

**File to Create:** `backend/src/core/security.py`

**Requirements:**
- Password hashing with bcrypt (minimum 12 rounds)
- JWT token creation with user_id, tenant_id, role, email in payload
- JWT token validation with proper error handling
- Secure random token generation for password resets
- Configurable token expiration via environment variables
- Type hints and comprehensive error handling

**Reference Files:**
- Use settings from `backend/src/core/config.py` for SECRET_KEY, ALGORITHM, ACCESS_TOKEN_EXPIRE_MINUTES
- Import required libraries: passlib, python-jose, datetime, typing

**Include:** Functions for get_password_hash, verify_password, create_access_token, verify_token, generate_reset_token
```

---

## 📋 Task 1.3: Create Authentication Dependencies

### 🎯 Effective Prompt
```
Create FastAPI dependencies for authentication and authorization with tenant context.

**File to Create:** `backend/src/core/deps.py`

**Requirements:**
- get_current_user: Extract and validate JWT token, return User object
- get_current_active_user: Ensure user is active
- get_current_admin_user: Enforce admin role or higher
- get_current_tenant: Retrieve user's tenant
- Proper HTTP exceptions for authentication failures
- Integration with tenant context for RLS

**Reference Files:**
- Import from `backend/src/core/security.py` for verify_token
- Import from `backend/src/core/database.py` for get_db
- Import from `backend/src/models/user.py` and `backend/src/models/tenant.py`
- Use `backend/src/core/tenant_context.py` for RLS context

**Include:** FastAPI dependencies with proper error handling and tenant isolation
```

---

## 📋 Task 1.4: Define Authentication Schemas

### 🎯 Effective Prompt
```
Create Pydantic schemas for authentication requests and responses with comprehensive validation.

**File to Create:** `backend/src/schemas/auth.py`

**Requirements:**
- UserCreate: email, password (min 8 chars), first_name, last_name, role, tenant_id
- UserUpdate: partial updates for profile fields
- UserInDB: complete user representation for API responses
- LoginRequest: email, password, optional tenant_slug
- Token: access_token, token_type, expires_in, user info
- Password reset schemas with secure token handling
- Proper validation rules and error messages

**Reference Files:**
- Check `backend/src/models/user.py` for field types and constraints
- Follow schema patterns from existing schemas in the project
- Use pydantic BaseModel, EmailStr, validator decorators

**Include:** Complete Pydantic schemas with validation, examples, and proper typing
```

---

## 📋 Task 1.5: Implement Authentication Service

### 🎯 Effective Prompt
```
Create comprehensive authentication service with user management and JWT token handling.

**File to Create:** `backend/src/services/auth_service.py`

**Requirements:**
- authenticate_user: validate email/password, support tenant-specific and owner login
- create_user: handle email uniqueness within tenant, password hashing
- create_access_token_for_user: generate JWT with proper claims
- Login tracking: update last_login and login_count
- Password verification with secure comparison
- Proper error handling for all failure cases

**Reference Files:**
- Import from `backend/src/models/user.py` and `backend/src/models/tenant.py`
- Use `backend/src/core/security.py` for password and token functions
- Import schemas from `backend/src/schemas/auth.py`
- Use `backend/src/core/tenant_context.py` for tenant isolation

**Include:** Complete service class with all authentication methods and error handling
```

---

## 📋 Task 1.6: Create Authentication API Endpoints

### 🎯 Effective Prompt
```
Create FastAPI endpoints for authentication operations with proper validation and error handling.

**File to Create:** `backend/src/api/v1/endpoints/auth.py`

**Requirements:**
- POST /login: Accept LoginRequest, return Token response
- POST /register: Create user, return UserInDB
- POST /token: OAuth2PasswordRequestForm compatibility
- GET /me: Return current user info
- Password reset endpoints: /forgot-password, /reset-password
- Proper HTTP status codes and error responses
- OpenAPI documentation with examples

**Reference Files:**
- Import from `backend/src/services/auth_service.py` for business logic
- Import schemas from `backend/src/schemas/auth.py`
- Use dependencies from `backend/src/core/deps.py`
- Follow endpoint patterns from existing API files

**Include:** Complete FastAPI router with all authentication endpoints and proper error handling
```

---

## 📋 Task 1.7: Add Authentication to Main App

### 🎯 Effective Prompt
```
Integrate authentication endpoints into the main FastAPI application with security configuration.

**Files to Modify:**
- `backend/src/api/v1/api.py` - Include auth router
- `backend/src/main.py` - Add security configuration, CORS, middleware

**Requirements:**
- Include auth router in API v1 routes with proper prefix and tags
- Configure CORS to allow authentication from frontend
- Add security headers middleware
- Configure OpenAPI security schemes for JWT
- Ensure application starts without errors

**Reference Files:**
- Import auth router from `backend/src/api/v1/endpoints/auth.py`
- Use settings from `backend/src/core/config.py` for CORS and security
- Follow existing router inclusion patterns

**Include:** Updated main.py with security configuration and api.py with auth router inclusion
```

---

## 📋 Task 1.8: Create Authentication Tests

### 🎯 Effective Prompt
```
Create comprehensive test suite for authentication functionality with high coverage.

**File to Create:** `backend/tests/test_auth.py`

**Requirements:**
- Unit tests for security utilities (password hashing, JWT tokens)
- Integration tests for authentication endpoints
- Test fixtures for users and authentication
- Test authentication with valid/invalid credentials
- Test role-based access control and tenant isolation
- Performance and security tests
- >95% test coverage

**Reference Files:**
- Import from all authentication modules: security, auth_service, endpoints
- Use test database and fixtures from existing test setup
- Follow testing patterns from existing test files

**Include:** Complete test suite with fixtures, unit tests, integration tests, and performance tests
```

---

## 📋 Task 1.9: Update Environment Configuration

### 🎯 Effective Prompt
```
Add authentication-specific environment variables and configuration validation.

**Files to Modify:**
- `backend/.env.example` - Add auth variables with examples
- `backend/src/core/config.py` - Add auth settings with validation

**Requirements:**
- SECRET_KEY for JWT signing (required, min 32 chars)
- ACCESS_TOKEN_EXPIRE_MINUTES (default 1440)
- ALGORITHM (default HS256)
- Password policy settings
- CORS and security settings
- Configuration validation with helpful error messages

**Reference Files:**
- Check existing config.py structure and validation patterns
- Follow environment variable naming conventions

**Include:** Updated .env.example and config.py with all authentication settings and validation
```

---

## 📋 Task 1.10: Authentication Integration and Validation

### 🎯 Effective Prompt
```
Create comprehensive validation script to ensure all authentication components work together.

**File to Create:** `backend/scripts/validate_auth_system.py`

**Requirements:**
- End-to-end authentication workflow validation
- Environment configuration validation
- Database schema and constraint validation
- Security utilities validation (password hashing, JWT)
- Authentication service validation
- API endpoint validation
- Performance benchmarking
- Comprehensive reporting of results

**Reference Files:**
- Import from all authentication modules for testing
- Use database connection from `backend/src/core/database.py`
- Use settings from `backend/src/core/config.py`

**Include:** Complete validation script with all checks and detailed reporting
```

---

## 📋 Task 1.11: Create User Database Migration

### 🎯 Effective Prompt
```
Create Alembic database migration for User table with all constraints and security policies.

**File to Create:** `backend/alembic/versions/001_create_users_table.py`

**Requirements:**
- User table with all fields from User model
- Unique constraint on (tenant_id, email) excluding soft deleted
- Performance indexes for email, tenant_id, role, login queries
- Row Level Security policies for tenant isolation
- Check constraints for data validation
- Triggers for timestamp updates
- Proper foreign key relationships

**Reference Files:**
- Use User model from `backend/src/models/user.py` for field definitions
- Follow existing migration patterns in alembic/versions/
- Reference tenant table structure for foreign keys

**Include:** Complete Alembic migration with upgrade/downgrade functions and validation
```

---

## 🔄 Implementation Order

### Phase 1: Foundation
1. **Task 1.9** → Environment Configuration
2. **Task 1.1** → User Data Model
3. **Task 1.11** → Database Migration

### Phase 2: Security Layer
4. **Task 1.2** → Security Utilities
5. **Task 1.3** → Authentication Dependencies

### Phase 3: Business Logic
6. **Task 1.4** → Authentication Schemas
7. **Task 1.5** → Authentication Service

### Phase 4: API Layer
8. **Task 1.6** → Authentication Endpoints
9. **Task 1.7** → Main App Integration

### Phase 5: Validation
10. **Task 1.8** → Comprehensive Tests
11. **Task 1.10** → Integration Validation

---

## 💡 Pro Tips for Implementation

### Context Gathering
- Always review existing files in the same directory for patterns
- Check imports and dependencies before implementing
- Look at similar implementations in the codebase

### File References
- Import statements should match existing project structure
- Follow naming conventions used in the project
- Maintain consistency with existing code style

### Testing Strategy
- Test each component as you build it
- Use the validation script to check integration
- Run tests frequently during development

### Error Handling
- Follow existing error handling patterns
- Use appropriate HTTP status codes
- Provide helpful error messages without leaking sensitive information
