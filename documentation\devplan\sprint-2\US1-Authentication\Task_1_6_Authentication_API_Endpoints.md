# Task 1.6: Create Authentication API Endpoints

## 📋 Task Overview
**User Story:** HU-2.1 - JWT Authentication System  
**Task ID:** 1.6  
**Estimated Time:** 2.5 hours  
**Priority:** Critical  
**Complexity:** Medium  

## 🎯 Description
Implement FastAPI endpoints for authentication operations that provide login, registration, and OAuth2-compatible token endpoints with proper error responses and comprehensive validation.

## 📦 Deliverables
- [ ] Create `backend/src/api/v1/endpoints/auth.py`
- [ ] Login endpoint with JWT token response
- [ ] User registration endpoint
- [ ] OAuth2-compatible token endpoint
- [ ] Proper error responses and status codes

## ✅ Acceptance Criteria
- [ ] POST /login accepts LoginRequest and returns Token
- [ ] POST /register creates new user and returns UserInDB
- [ ] POST /token supports OAuth2PasswordRequestForm
- [ ] All endpoints return proper HTTP status codes
- [ ] Error responses include helpful messages
- [ ] Endpoints are properly documented with OpenAPI
- [ ] Rate limiting considerations are documented

## 🔧 Technical Requirements

### Endpoint Specifications
1. **POST /auth/login**
   - Accept LoginRequest (email, password, tenant_slug)
   - Return Token with access_token and user info
   - Support both tenant-specific and owner login

2. **POST /auth/register**
   - Accept UserCreate schema
   - Create new user account
   - Return user information

3. **POST /auth/token**
   - OAuth2PasswordRequestForm compatibility
   - Standard OAuth2 token response
   - Support for client credentials

### Security Requirements
- Input validation and sanitization
- Rate limiting for authentication attempts
- Secure error messages (no user enumeration)
- Proper HTTP status codes

## 🔗 Dependencies
- **Prerequisite:** AuthService (Task 1.5), Authentication schemas (Task 1.4)
- **Services:** AuthService for business logic
- **Security:** Authentication dependencies

## 📝 Code Example
```python
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from datetime import timedelta

from ....core.database import get_db
from ....core.config import settings
from ....schemas.auth import LoginRequest, Token, UserCreate, UserInDB
from ....services.auth_service import AuthService

router = APIRouter()

@router.post("/login", response_model=Token)
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """Authenticate user and return access token"""
    auth_service = AuthService(db)
    
    # Authenticate user
    user = auth_service.authenticate_user(login_data)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create access token
    token_response = auth_service.create_access_token_for_user(user)
    return token_response

@router.post("/register", response_model=UserInDB)
async def register(
    user_data: UserCreate,
    db: Session = Depends(get_db)
):
    """Register a new user"""
    auth_service = AuthService(db)
    
    try:
        user = auth_service.create_user(user_data)
        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user account"
        )

@router.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """OAuth2 compatible token endpoint"""
    auth_service = AuthService(db)
    
    # Convert OAuth2 form to LoginRequest
    login_data = LoginRequest(
        email=form_data.username,  # OAuth2 uses 'username' field
        password=form_data.password,
        tenant_slug=form_data.client_id  # Use client_id as tenant_slug
    )
    
    # Authenticate user
    user = auth_service.authenticate_user(login_data)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create access token
    token_response = auth_service.create_access_token_for_user(user)
    return token_response

@router.post("/refresh")
async def refresh_token(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Refresh access token"""
    auth_service = AuthService(db)
    token_response = auth_service.create_access_token_for_user(current_user)
    return token_response

@router.post("/logout")
async def logout(
    current_user: User = Depends(get_current_user)
):
    """Logout user (client-side token removal)"""
    # In a stateless JWT system, logout is handled client-side
    # This endpoint can be used for logging/analytics
    return {"message": "Successfully logged out"}

@router.get("/me", response_model=UserInDB)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """Get current user information"""
    return current_user

@router.post("/verify-email")
async def verify_email(
    token: str,
    db: Session = Depends(get_db)
):
    """Verify user email with token"""
    auth_service = AuthService(db)
    
    success = auth_service.verify_user_email(token)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification token"
        )
    
    return {"message": "Email verified successfully"}

@router.post("/forgot-password")
async def forgot_password(
    email: str,
    tenant_slug: str,
    db: Session = Depends(get_db)
):
    """Request password reset"""
    auth_service = AuthService(db)
    
    success = auth_service.request_password_reset(email, tenant_slug)
    # Always return success to prevent user enumeration
    return {"message": "If the email exists, a password reset link has been sent"}

@router.post("/reset-password")
async def reset_password(
    token: str,
    new_password: str,
    db: Session = Depends(get_db)
):
    """Reset password with token"""
    auth_service = AuthService(db)
    
    success = auth_service.reset_password(token, new_password)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )
    
    return {"message": "Password reset successfully"}

@router.post("/change-password")
async def change_password(
    current_password: str,
    new_password: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Change user password"""
    auth_service = AuthService(db)
    
    # Verify current password
    if not auth_service.verify_password(current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Update password
    success = auth_service.change_password(current_user.id, new_password)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change password"
        )
    
    return {"message": "Password changed successfully"}
```

## 🧪 Testing Requirements
- [ ] Test successful login with valid credentials
- [ ] Test login failure with invalid credentials
- [ ] Test user registration with valid data
- [ ] Test registration with duplicate email
- [ ] Test OAuth2 token endpoint compatibility
- [ ] Test password reset workflow
- [ ] Test email verification workflow
- [ ] Test error handling and status codes
- [ ] Test rate limiting behavior

## 📊 Validation Checklist
- [ ] All endpoints return proper HTTP status codes
- [ ] Error messages are helpful but secure
- [ ] Input validation prevents malicious data
- [ ] OAuth2 compatibility is maintained
- [ ] API documentation is complete
- [ ] Rate limiting is implemented
- [ ] Security headers are set

## 🚨 Security Considerations
- [ ] Prevent user enumeration through error messages
- [ ] Implement rate limiting for authentication
- [ ] Validate all input parameters
- [ ] Use secure password policies
- [ ] Log authentication attempts
- [ ] Protect against brute force attacks

## 📈 Performance Considerations
- [ ] Authentication response time <200ms
- [ ] Efficient database queries
- [ ] Minimal token generation overhead
- [ ] Proper error handling without performance impact

## 🎯 Definition of Done
- [ ] All authentication endpoints are implemented
- [ ] Login and registration work correctly
- [ ] OAuth2 compatibility is maintained
- [ ] Error handling is comprehensive
- [ ] Security measures are in place
- [ ] API documentation is complete
- [ ] All tests pass
- [ ] Code review is completed
