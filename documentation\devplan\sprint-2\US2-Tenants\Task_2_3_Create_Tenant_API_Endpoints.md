# Task 2.3: Create Tenant API Endpoints

## 📋 Task Overview
**User Story:** HU-2.2 - Tenant CRUD Operations  
**Task ID:** 2.3  
**Estimated Time:** 3 hours  
**Priority:** Critical  
**Complexity:** Medium  

## 🎯 Description
Implement FastAPI endpoints for tenant management that provide proper role-based access control, comprehensive error handling, and support both current tenant operations and system-wide tenant management.

## 📦 Deliverables
- [ ] Create `backend/src/api/v1/endpoints/tenants.py`
- [ ] Current tenant information endpoints
- [ ] System owner tenant management endpoints
- [ ] Tenant statistics and analytics endpoints
- [ ] Proper authorization and error handling

## ✅ Acceptance Criteria
- [ ] GET /current returns current user's tenant info
- [ ] PUT /current allows tenant admin to update settings
- [ ] GET / returns all tenants (system owner only)
- [ ] GET /{tenant_id} returns specific tenant (system owner only)
- [ ] POST / creates new tenant (system owner only)
- [ ] DELETE /{tenant_id} soft deletes tenant (system owner only)
- [ ] All endpoints enforce proper role-based permissions
- [ ] Proper HTTP status codes and error responses

## 🔧 Technical Requirements

### Endpoint Categories
1. **Current Tenant Endpoints** (Admin access)
   - GET /tenants/current
   - PUT /tenants/current

2. **System Management Endpoints** (Owner only)
   - GET /tenants
   - POST /tenants
   - GET /tenants/{tenant_id}
   - DELETE /tenants/{tenant_id}

### Security Requirements
- Role-based access control
- Tenant isolation enforcement
- Input validation and sanitization
- Proper error handling without information disclosure

## 🔗 Dependencies
- **Prerequisite:** TenantService (Task 2.2), Authentication dependencies, RBAC system
- **Services:** TenantService for business logic
- **Security:** Role-based access control decorators

## 📝 Code Example
```python
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List

from ....core.database import get_db
from ....core.deps import get_current_admin_user, get_current_tenant
from ....models.user import User
from ....models.tenant import Tenant
from ....schemas.tenant import TenantInDB, TenantUpdate, TenantWithStats, TenantCreate
from ....services.tenant_service import TenantService

router = APIRouter()

@router.get("/current", response_model=TenantInDB)
async def get_current_tenant_info(
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """Get current tenant information"""
    return current_tenant

@router.put("/current", response_model=TenantInDB)
async def update_current_tenant(
    tenant_data: TenantUpdate,
    current_user: User = Depends(get_current_admin_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: Session = Depends(get_db)
):
    """Update current tenant information"""
    tenant_service = TenantService(db)
    return tenant_service.update_tenant(current_tenant.id, tenant_data)

# System owner only endpoints
@router.get("/", response_model=List[TenantWithStats])
async def get_all_tenants(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get all tenants (system owner only)"""
    if current_user.role != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only system owners can access this endpoint"
        )

    tenant_service = TenantService(db)
    return tenant_service.get_tenants_with_stats(skip, limit)

@router.post("/", response_model=TenantInDB)
async def create_tenant(
    tenant_data: TenantCreate,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Create new tenant (system owner only)"""
    if current_user.role != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only system owners can create tenants"
        )

    tenant_service = TenantService(db)
    return tenant_service.create_tenant(tenant_data)

@router.get("/{tenant_id}", response_model=TenantInDB)
async def get_tenant(
    tenant_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Get specific tenant (system owner only)"""
    if current_user.role != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only system owners can access this endpoint"
        )

    tenant_service = TenantService(db)
    tenant = tenant_service.get_tenant_by_id(tenant_id)

    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tenant not found"
        )

    return tenant

@router.delete("/{tenant_id}")
async def delete_tenant(
    tenant_id: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Soft delete tenant (system owner only)"""
    if current_user.role != "owner":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only system owners can delete tenants"
        )

    tenant_service = TenantService(db)
    success = tenant_service.delete_tenant(tenant_id)

    if success:
        return {"message": "Tenant deleted successfully"}
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete tenant"
        )
```

## 🎯 Definition of Done
- [ ] All tenant API endpoints are implemented
- [ ] Role-based access control is enforced
- [ ] Error handling is comprehensive
- [ ] API documentation is complete
- [ ] All tests pass
- [ ] Code review is completed
