# Task 5.1: Create Admin Dashboard Layout

## 📋 Task Overview
**User Story:** HU-2.5 - Admin Interface  
**Task ID:** 5.1  
**Estimated Time:** 2 hours  
**Priority:** High  
**Complexity:** Medium  

## 🎯 Description
Design and implement the main admin dashboard structure that provides a responsive layout with navigation, overview metrics, and role-based menu items for tenant administrators.

## 📦 Deliverables
- [ ] Create `frontend/src/pages/admin/Dashboard.tsx`
- [ ] Admin layout component with navigation
- [ ] Dashboard overview with key metrics
- [ ] Responsive design for mobile and desktop
- [ ] Role-based navigation menu

## ✅ Acceptance Criteria
- [ ] Admin dashboard accessible only to admin/owner roles
- [ ] Navigation sidebar with admin sections
- [ ] Overview cards showing user count, activity, etc.
- [ ] Responsive layout works on mobile and desktop
- [ ] Role-based menu items (owner sees more options)
- [ ] Loading states and error handling
- [ ] Consistent styling with design system

## 🔧 Technical Requirements

### Component Structure
1. **AdminLayout Component**
   - Sidebar navigation
   - Header with user info
   - Main content area
   - Mobile-responsive design

2. **Dashboard Overview**
   - Metric cards (users, activity, etc.)
   - Quick action buttons
   - Recent activity feed
   - Charts and visualizations

3. **Navigation Menu**
   - Role-based menu items
   - Active state highlighting
   - Collapsible sidebar
   - Mobile hamburger menu

## 🔗 Dependencies
- **Prerequisite:** Authentication system, User management APIs
- **UI Framework:** React, TypeScript, Tailwind CSS
- **State Management:** React Context or Redux
- **Charts:** Chart.js or similar library

## 📝 Code Example
```typescript
import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useApi } from '../../hooks/useApi';
import { AdminLayout } from '../../components/admin/AdminLayout';
import { MetricCard } from '../../components/admin/MetricCard';
import { ActivityFeed } from '../../components/admin/ActivityFeed';
import { QuickActions } from '../../components/admin/QuickActions';
import { UserChart } from '../../components/admin/UserChart';

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  newUsersThisMonth: number;
  totalLogins: number;
  recentActivity: ActivityItem[];
}

interface ActivityItem {
  id: string;
  type: string;
  description: string;
  timestamp: string;
  user: {
    name: string;
    email: string;
  };
}

export const AdminDashboard: React.FC = () => {
  const { user } = useAuth();
  const { get } = useApi();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const [userStats, activity] = await Promise.all([
        get('/users/stats/overview'),
        get('/activity/recent?limit=10')
      ]);

      setStats({
        totalUsers: userStats.total_users,
        activeUsers: userStats.active_users,
        newUsersThisMonth: userStats.recent_registrations,
        totalLogins: userStats.recent_logins,
        recentActivity: activity
      });
    } catch (err) {
      setError('Failed to load dashboard data');
      console.error('Dashboard error:', err);
    } finally {
      setLoading(false);
    }
  };

  if (!user?.is_admin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access the admin dashboard.</p>
        </div>
      </div>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-gray-600">Welcome back, {user.full_name}</p>
            </div>
            <QuickActions onRefresh={loadDashboardData} />
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Loading dashboard...</span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error loading dashboard</h3>
                <p className="text-sm text-red-700 mt-1">{error}</p>
                <button
                  onClick={loadDashboardData}
                  className="mt-2 text-sm text-red-800 underline hover:text-red-900"
                >
                  Try again
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Dashboard Content */}
        {stats && !loading && (
          <>
            {/* Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <MetricCard
                title="Total Users"
                value={stats.totalUsers}
                icon="users"
                color="blue"
                trend={{ value: stats.newUsersThisMonth, label: "new this month" }}
              />
              <MetricCard
                title="Active Users"
                value={stats.activeUsers}
                icon="user-check"
                color="green"
                percentage={Math.round((stats.activeUsers / stats.totalUsers) * 100)}
              />
              <MetricCard
                title="New Users"
                value={stats.newUsersThisMonth}
                icon="user-plus"
                color="purple"
                trend={{ value: 12, label: "vs last month", isPositive: true }}
              />
              <MetricCard
                title="Total Logins"
                value={stats.totalLogins}
                icon="login"
                color="orange"
                trend={{ value: 8, label: "vs last week", isPositive: true }}
              />
            </div>

            {/* Charts and Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* User Growth Chart */}
              <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">User Growth</h2>
                <UserChart />
              </div>

              {/* Recent Activity */}
              <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h2>
                <ActivityFeed activities={stats.recentActivity} />
              </div>
            </div>
          </>
        )}
      </div>
    </AdminLayout>
  );
};

// AdminLayout Component
export const AdminLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();

  const navigation = [
    { name: 'Dashboard', href: '/admin', icon: 'home', current: true },
    { name: 'Users', href: '/admin/users', icon: 'users', current: false },
    { name: 'Settings', href: '/admin/settings', icon: 'cog', current: false },
    { name: 'Analytics', href: '/admin/analytics', icon: 'chart-bar', current: false },
  ];

  // Add owner-only navigation items
  if (user?.role === 'owner') {
    navigation.push(
      { name: 'Tenants', href: '/admin/tenants', icon: 'office-building', current: false },
      { name: 'System Settings', href: '/admin/system', icon: 'adjustments', current: false }
    );
  }

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'block' : 'hidden'} fixed inset-0 flex z-40 md:hidden`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={() => setSidebarOpen(false)}
            >
              <span className="sr-only">Close sidebar</span>
              <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <SidebarContent navigation={navigation} user={user} onLogout={logout} />
        </div>
      </div>

      {/* Static sidebar for desktop */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64">
          <SidebarContent navigation={navigation} user={user} onLogout={logout} />
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* Mobile header */}
        <div className="md:hidden pl-1 pt-1 sm:pl-3 sm:pt-3">
          <button
            className="-ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500"
            onClick={() => setSidebarOpen(true)}
          >
            <span className="sr-only">Open sidebar</span>
            <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Main content area */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
```

## 🎯 Definition of Done
- [ ] Admin dashboard layout is implemented
- [ ] Navigation sidebar works on desktop and mobile
- [ ] Overview metrics are displayed correctly
- [ ] Role-based navigation is functional
- [ ] Responsive design works across devices
- [ ] Loading and error states are handled
- [ ] Component is tested and documented
- [ ] Code review is completed
