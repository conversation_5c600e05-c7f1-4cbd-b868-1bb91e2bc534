# Task 1.11: Create User Database Migration

## 📋 Task Overview
**User Story:** HU-2.1 - JWT Authentication System  
**Task ID:** 1.11  
**Estimated Time:** 1 hour  
**Priority:** Critical  
**Complexity:** Low  

## 🎯 Description
Create Alembic database migration for the User table with all necessary constraints, indexes, and Row Level Security (RLS) policies to ensure proper data isolation and performance.

## 📦 Deliverables
- [ ] Create Alembic migration file for User table
- [ ] Add proper indexes for performance
- [ ] Implement Row Level Security policies
- [ ] Add database constraints for data integrity
- [ ] Create migration rollback procedures

## ✅ Acceptance Criteria
- [ ] User table is created with all required fields
- [ ] Email uniqueness constraint within tenant
- [ ] Proper indexes for email, tenant_id, and role
- [ ] RLS policies enforce tenant isolation
- [ ] Foreign key constraints are properly defined
- [ ] Migration can be applied and rolled back safely
- [ ] Performance indexes are optimized for auth queries

## 🔧 Technical Requirements

### Migration Structure
1. **Table Creation**
   - All User model fields
   - Proper data types and constraints
   - Default values where appropriate

2. **Indexes**
   - Unique index on (tenant_id, email)
   - Index on tenant_id for RLS
   - Index on role for filtering
   - Index on is_active for queries

3. **Row Level Security**
   - Enable RLS on users table
   - Policy for tenant isolation
   - Policy for user self-access

## 🔗 Dependencies
- **Prerequisite:** User model (Task 1.1), Database setup from Sprint 1
- **Database:** PostgreSQL with RLS support
- **Migration:** Alembic configuration

## 📝 Code Example
```python
"""Create users table with authentication fields

Revision ID: 001_create_users_table
Revises: 
Create Date: 2024-01-08 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import uuid

# revision identifiers
revision = '001_create_users_table'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    """Create users table with all authentication and profile fields"""
    
    # Create users table
    op.create_table(
        'users',
        # Primary key and UUID
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        
        # Tenant relationship
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        
        # Authentication fields
        sa.Column('email', sa.String(255), nullable=False),
        sa.Column('hashed_password', sa.String(255), nullable=False),
        sa.Column('role', sa.String(50), nullable=False, default='user'),
        sa.Column('is_active', sa.Boolean(), nullable=False, default=True),
        sa.Column('is_verified', sa.Boolean(), nullable=False, default=False),
        
        # Profile fields
        sa.Column('username', sa.String(100), nullable=True),
        sa.Column('first_name', sa.String(100), nullable=True),
        sa.Column('last_name', sa.String(100), nullable=True),
        sa.Column('avatar_url', sa.String(500), nullable=True),
        sa.Column('bio', sa.String(500), nullable=True),
        
        # Login tracking
        sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
        sa.Column('login_count', sa.Integer(), nullable=False, default=0),
        
        # Timestamps
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')),
        
        # Soft delete
        sa.Column('is_deleted', sa.Boolean(), nullable=False, default=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    )
    
    # Create indexes for performance
    
    # Unique constraint on email within tenant (excluding soft deleted)
    op.create_index(
        'idx_users_tenant_email_unique',
        'users',
        ['tenant_id', 'email'],
        unique=True,
        postgresql_where=sa.text('is_deleted = false')
    )
    
    # Index for tenant-based queries (RLS)
    op.create_index(
        'idx_users_tenant_id',
        'users',
        ['tenant_id']
    )
    
    # Index for authentication queries
    op.create_index(
        'idx_users_email',
        'users',
        ['email']
    )
    
    # Index for role-based queries
    op.create_index(
        'idx_users_role',
        'users',
        ['role']
    )
    
    # Index for active user queries
    op.create_index(
        'idx_users_active',
        'users',
        ['is_active', 'is_deleted']
    )
    
    # Index for username (if used)
    op.create_index(
        'idx_users_username',
        'users',
        ['username'],
        postgresql_where=sa.text('username IS NOT NULL AND is_deleted = false')
    )
    
    # Composite index for common queries
    op.create_index(
        'idx_users_tenant_active',
        'users',
        ['tenant_id', 'is_active', 'is_deleted']
    )
    
    # Index for login tracking queries
    op.create_index(
        'idx_users_last_login',
        'users',
        ['last_login']
    )
    
    # Add foreign key constraint to tenants table (if exists)
    # Note: This assumes tenants table exists from previous migration
    try:
        op.create_foreign_key(
            'fk_users_tenant_id',
            'users',
            'tenants',
            ['tenant_id'],
            ['id'],
            ondelete='CASCADE'
        )
    except Exception:
        # If tenants table doesn't exist yet, skip foreign key
        # It will be added in a later migration
        pass
    
    # Enable Row Level Security
    op.execute('ALTER TABLE users ENABLE ROW LEVEL SECURITY')
    
    # Create RLS policy for tenant isolation
    op.execute("""
        CREATE POLICY tenant_isolation_policy ON users
        FOR ALL
        TO authenticated
        USING (tenant_id = current_setting('app.current_tenant_id')::uuid)
        WITH CHECK (tenant_id = current_setting('app.current_tenant_id')::uuid)
    """)
    
    # Create RLS policy for system owners (can access all tenants)
    op.execute("""
        CREATE POLICY system_owner_policy ON users
        FOR ALL
        TO authenticated
        USING (
            EXISTS (
                SELECT 1 FROM users owner_user 
                WHERE owner_user.id = current_setting('app.current_user_id')::uuid 
                AND owner_user.role = 'owner'
            )
        )
    """)
    
    # Create RLS policy for user self-access
    op.execute("""
        CREATE POLICY user_self_access_policy ON users
        FOR ALL
        TO authenticated
        USING (id = current_setting('app.current_user_id')::uuid)
        WITH CHECK (id = current_setting('app.current_user_id')::uuid)
    """)
    
    # Create trigger for updating updated_at timestamp
    op.execute("""
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
    """)
    
    op.execute("""
        CREATE TRIGGER update_users_updated_at
        BEFORE UPDATE ON users
        FOR EACH ROW
        EXECUTE FUNCTION update_updated_at_column();
    """)
    
    # Add check constraints for data validation
    op.create_check_constraint(
        'check_users_role_valid',
        'users',
        sa.text("role IN ('owner', 'admin', 'user')")
    )
    
    op.create_check_constraint(
        'check_users_email_format',
        'users',
        sa.text("email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'")
    )
    
    op.create_check_constraint(
        'check_users_login_count_positive',
        'users',
        sa.text('login_count >= 0')
    )
    
    # Add comments for documentation
    op.execute("COMMENT ON TABLE users IS 'User accounts with authentication and profile information'")
    op.execute("COMMENT ON COLUMN users.tenant_id IS 'Reference to tenant for multi-tenancy'")
    op.execute("COMMENT ON COLUMN users.email IS 'User email address, unique within tenant'")
    op.execute("COMMENT ON COLUMN users.hashed_password IS 'Bcrypt hashed password'")
    op.execute("COMMENT ON COLUMN users.role IS 'User role: owner, admin, or user'")
    op.execute("COMMENT ON COLUMN users.login_count IS 'Number of successful logins'")
    op.execute("COMMENT ON COLUMN users.last_login IS 'Timestamp of last successful login'")

def downgrade():
    """Drop users table and related objects"""
    
    # Drop triggers
    op.execute('DROP TRIGGER IF EXISTS update_users_updated_at ON users')
    op.execute('DROP FUNCTION IF EXISTS update_updated_at_column()')
    
    # Drop RLS policies
    op.execute('DROP POLICY IF EXISTS tenant_isolation_policy ON users')
    op.execute('DROP POLICY IF EXISTS system_owner_policy ON users')
    op.execute('DROP POLICY IF EXISTS user_self_access_policy ON users')
    
    # Drop indexes (foreign keys and constraints are dropped with table)
    op.drop_index('idx_users_last_login', table_name='users')
    op.drop_index('idx_users_tenant_active', table_name='users')
    op.drop_index('idx_users_username', table_name='users')
    op.drop_index('idx_users_active', table_name='users')
    op.drop_index('idx_users_role', table_name='users')
    op.drop_index('idx_users_email', table_name='users')
    op.drop_index('idx_users_tenant_id', table_name='users')
    op.drop_index('idx_users_tenant_email_unique', table_name='users')
    
    # Drop table
    op.drop_table('users')

# Migration validation functions
def validate_migration():
    """Validate that migration was applied correctly"""
    from sqlalchemy import create_engine, text
    from core.config import settings
    
    engine = create_engine(settings.DATABASE_URL)
    
    with engine.connect() as conn:
        # Check table exists
        result = conn.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'users'
            );
        """))
        
        if not result.scalar():
            raise Exception("Users table was not created")
        
        # Check RLS is enabled
        result = conn.execute(text("""
            SELECT relrowsecurity FROM pg_class 
            WHERE relname = 'users'
        """))
        
        if not result.scalar():
            raise Exception("Row Level Security is not enabled on users table")
        
        # Check indexes exist
        result = conn.execute(text("""
            SELECT count(*) FROM pg_indexes 
            WHERE tablename = 'users'
        """))
        
        index_count = result.scalar()
        if index_count < 6:  # Should have at least 6 indexes
            raise Exception(f"Expected at least 6 indexes, found {index_count}")
        
        print("✅ Users table migration validation passed")

if __name__ == "__main__":
    # Run validation if executed directly
    validate_migration()
```

## 🧪 Testing Requirements
- [ ] Test migration applies successfully
- [ ] Test migration rollback works correctly
- [ ] Validate all indexes are created
- [ ] Test RLS policies work correctly
- [ ] Verify constraints prevent invalid data
- [ ] Test foreign key relationships
- [ ] Performance test with sample data

## 📊 Validation Checklist
- [ ] Users table is created with all fields
- [ ] Unique constraint on (tenant_id, email) works
- [ ] All indexes are properly created
- [ ] RLS policies enforce tenant isolation
- [ ] Check constraints validate data
- [ ] Triggers update timestamps correctly
- [ ] Migration can be rolled back safely

## 🚨 Security Considerations
- [ ] RLS policies prevent cross-tenant access
- [ ] Email constraint prevents duplicates
- [ ] Password field is properly secured
- [ ] Soft delete preserves data integrity
- [ ] Audit trail is maintained

## 📈 Performance Considerations
- [ ] Indexes optimize authentication queries
- [ ] Composite indexes support common query patterns
- [ ] RLS policies are efficiently implemented
- [ ] Constraints don't impact performance significantly

## 🎯 Definition of Done
- [ ] Migration file is created and tested
- [ ] Users table is properly structured
- [ ] All indexes and constraints work
- [ ] RLS policies enforce security
- [ ] Migration validation passes
- [ ] Rollback procedure works
- [ ] Performance requirements are met
- [ ] Code review is completed
