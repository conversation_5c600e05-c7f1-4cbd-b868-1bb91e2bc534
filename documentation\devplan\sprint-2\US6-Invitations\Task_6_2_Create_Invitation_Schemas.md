# Task 6.2: Create Invitation Schemas

## 📋 Task Overview
**User Story:** HU-2.6 - Invitation System  
**Task ID:** 6.2  
**Estimated Time:** 1.5 hours  
**Priority:** High  
**Complexity:** Medium  

## 🎯 Description
Define Pydantic schemas for invitation operations that provide proper validation, serialization, and API documentation for invitation creation, acceptance, and management workflows.

## 📦 Deliverables
- [ ] Create `backend/src/schemas/invitation.py`
- [ ] Invitation creation and management schemas
- [ ] Invitation acceptance schemas
- [ ] Invitation response schemas with status
- [ ] Proper validation rules for all fields

## ✅ Acceptance Criteria
- [ ] InvitationCreate schema with email and role validation
- [ ] InvitationAccept schema for invitation acceptance workflow
- [ ] InvitationInDB schema for API responses
- [ ] InvitationUpdate schema for invitation management
- [ ] Proper validation for email format and role values
- [ ] Token validation and expiration handling
- [ ] Error handling for invalid invitation data

## 🔧 Technical Requirements

### Schema Categories
1. **Creation Schemas**
   - InvitationCreate for sending invitations
   - BulkInvitationCreate for multiple invitations

2. **Response Schemas**
   - InvitationInDB for API responses
   - InvitationWithStats for analytics

3. **Acceptance Schemas**
   - InvitationAccept for user registration
   - InvitationValidation for token checking

4. **Management Schemas**
   - InvitationUpdate for invitation modifications
   - InvitationFilter for searching invitations

### Validation Rules
- Email format validation
- Role validation (owner, admin, user)
- Token format and security validation
- Expiration date validation
- Message length limits

## 🔗 Dependencies
- **Prerequisite:** Pydantic library, User and Tenant schemas
- **Models:** Invitation model structure for reference
- **Validation:** Email and role validation utilities

## 📝 Code Example
```python
from pydantic import BaseModel, EmailStr, validator, Field
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
from enum import Enum

class InvitationStatus(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    EXPIRED = "expired"
    CANCELLED = "cancelled"

class UserRole(str, Enum):
    OWNER = "owner"
    ADMIN = "admin"
    USER = "user"

class InvitationBase(BaseModel):
    email: EmailStr
    role: UserRole = UserRole.USER
    message: Optional[str] = Field(None, max_length=500)

class InvitationCreate(InvitationBase):
    """Schema for creating a new invitation"""
    expires_in_days: Optional[int] = Field(7, ge=1, le=30)
    metadata: Optional[Dict[str, Any]] = None
    
    @validator('message')
    def validate_message(cls, v):
        if v is not None and len(v.strip()) == 0:
            return None
        return v
    
    @validator('metadata')
    def validate_metadata(cls, v):
        if v is not None:
            # Ensure metadata doesn't contain sensitive information
            sensitive_keys = ['password', 'token', 'secret', 'key']
            for key in v.keys():
                if any(sensitive in key.lower() for sensitive in sensitive_keys):
                    raise ValueError(f'Metadata cannot contain sensitive key: {key}')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "role": "user",
                "message": "Welcome to our platform! Please accept this invitation to get started.",
                "expires_in_days": 7,
                "metadata": {
                    "department": "Engineering",
                    "team": "Backend"
                }
            }
        }

class BulkInvitationCreate(BaseModel):
    """Schema for creating multiple invitations"""
    invitations: List[InvitationCreate]
    send_emails: bool = True
    
    @validator('invitations')
    def validate_invitations_limit(cls, v):
        if len(v) > 100:
            raise ValueError('Cannot create more than 100 invitations at once')
        if len(v) == 0:
            raise ValueError('At least one invitation must be provided')
        
        # Check for duplicate emails
        emails = [inv.email for inv in v]
        if len(emails) != len(set(emails)):
            raise ValueError('Duplicate emails found in invitation list')
        
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "invitations": [
                    {
                        "email": "<EMAIL>",
                        "role": "user",
                        "message": "Welcome to the team!"
                    },
                    {
                        "email": "<EMAIL>",
                        "role": "admin",
                        "message": "You've been invited as an administrator."
                    }
                ],
                "send_emails": True
            }
        }

class InvitationUpdate(BaseModel):
    """Schema for updating an invitation"""
    message: Optional[str] = Field(None, max_length=500)
    expires_at: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None
    
    @validator('expires_at')
    def validate_expires_at(cls, v):
        if v is not None and v <= datetime.utcnow():
            raise ValueError('Expiration date must be in the future')
        return v

class InvitationInDB(InvitationBase):
    """Schema for invitation database representation"""
    id: str
    tenant_id: str
    token: str
    status: InvitationStatus
    expires_at: datetime
    created_at: datetime
    updated_at: datetime
    
    # Usage tracking
    accepted_at: Optional[datetime] = None
    accepted_by: Optional[str] = None
    
    # Invitation context
    invited_by: str
    metadata: Optional[Dict[str, Any]] = None
    
    # Computed properties
    is_expired: bool = False
    is_valid: bool = False
    days_until_expiry: Optional[int] = None
    
    @validator('is_expired', pre=True, always=True)
    def set_is_expired(cls, v, values):
        expires_at = values.get('expires_at')
        if expires_at:
            return datetime.utcnow() > expires_at
        return False
    
    @validator('is_valid', pre=True, always=True)
    def set_is_valid(cls, v, values):
        status = values.get('status')
        is_expired = values.get('is_expired', False)
        return status == InvitationStatus.PENDING and not is_expired
    
    @validator('days_until_expiry', pre=True, always=True)
    def set_days_until_expiry(cls, v, values):
        expires_at = values.get('expires_at')
        if expires_at and expires_at > datetime.utcnow():
            delta = expires_at - datetime.utcnow()
            return delta.days
        return None
    
    class Config:
        from_attributes = True
        schema_extra = {
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "email": "<EMAIL>",
                "role": "user",
                "status": "pending",
                "token": "secure-invitation-token-here",
                "expires_at": "2024-01-15T10:30:00Z",
                "created_at": "2024-01-08T10:30:00Z",
                "is_expired": False,
                "is_valid": True,
                "days_until_expiry": 7
            }
        }

class InvitationAccept(BaseModel):
    """Schema for accepting an invitation"""
    token: str
    password: str = Field(..., min_length=8)
    first_name: Optional[str] = Field(None, max_length=100)
    last_name: Optional[str] = Field(None, max_length=100)
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        if not any(c.isalpha() for c in v):
            raise ValueError('Password must contain at least one letter')
        return v
    
    @validator('username')
    def validate_username(cls, v):
        if v is not None:
            if not v.replace('_', '').replace('-', '').isalnum():
                raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
        return v
    
    class Config:
        schema_extra = {
            "example": {
                "token": "secure-invitation-token-here",
                "password": "securepassword123",
                "first_name": "John",
                "last_name": "Doe",
                "username": "johndoe"
            }
        }

class InvitationValidation(BaseModel):
    """Schema for invitation token validation response"""
    valid: bool
    invitation: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    
    class Config:
        schema_extra = {
            "example": {
                "valid": True,
                "invitation": {
                    "email": "<EMAIL>",
                    "role": "user",
                    "tenant_name": "Acme Corporation",
                    "expires_at": "2024-01-15T10:30:00Z",
                    "message": "Welcome to our platform!"
                },
                "error": None
            }
        }

class InvitationFilter(BaseModel):
    """Schema for filtering invitations"""
    status: Optional[InvitationStatus] = None
    role: Optional[UserRole] = None
    email_search: Optional[str] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    expires_after: Optional[datetime] = None
    expires_before: Optional[datetime] = None
    invited_by: Optional[str] = None
    
    class Config:
        schema_extra = {
            "example": {
                "status": "pending",
                "role": "user",
                "email_search": "john",
                "created_after": "2024-01-01T00:00:00Z"
            }
        }

class InvitationStats(BaseModel):
    """Schema for invitation statistics"""
    total: int
    pending: int
    accepted: int
    expired: int
    cancelled: int
    recent: int  # Last 30 days
    acceptance_rate: float  # Percentage
    
    class Config:
        schema_extra = {
            "example": {
                "total": 150,
                "pending": 25,
                "accepted": 100,
                "expired": 20,
                "cancelled": 5,
                "recent": 30,
                "acceptance_rate": 66.7
            }
        }

class InvitationListResponse(BaseModel):
    """Schema for paginated invitation list response"""
    invitations: List[InvitationInDB]
    total_count: int
    page: int
    pages: int
    has_next: bool
    has_prev: bool
    
    class Config:
        schema_extra = {
            "example": {
                "invitations": [],
                "total_count": 150,
                "page": 1,
                "pages": 15,
                "has_next": True,
                "has_prev": False
            }
        }

class BulkInvitationResult(BaseModel):
    """Schema for bulk invitation operation results"""
    total: int
    successful: int
    failed: int
    errors: List[Dict[str, Any]] = []
    invitations: List[InvitationInDB] = []
    
    class Config:
        schema_extra = {
            "example": {
                "total": 10,
                "successful": 8,
                "failed": 2,
                "errors": [
                    {
                        "email": "invalid@email",
                        "error": "Invalid email format"
                    },
                    {
                        "email": "<EMAIL>",
                        "error": "User already exists"
                    }
                ],
                "invitations": []
            }
        }

class InvitationResend(BaseModel):
    """Schema for resending invitations"""
    regenerate_token: bool = False
    extend_expiry: bool = True
    new_message: Optional[str] = Field(None, max_length=500)
    
    class Config:
        schema_extra = {
            "example": {
                "regenerate_token": True,
                "extend_expiry": True,
                "new_message": "Reminder: Please accept your invitation to join our platform."
            }
        }

# Email template data schema
class InvitationEmailData(BaseModel):
    """Schema for invitation email template data"""
    to_email: EmailStr
    invitation_token: str
    tenant_name: str
    tenant_slug: str
    inviter_name: str
    role: str
    message: Optional[str] = None
    expires_at: datetime
    tenant_branding: Optional[Dict[str, Any]] = None
    
    class Config:
        schema_extra = {
            "example": {
                "to_email": "<EMAIL>",
                "invitation_token": "secure-token-here",
                "tenant_name": "Acme Corporation",
                "tenant_slug": "acme-corp",
                "inviter_name": "John Admin",
                "role": "user",
                "message": "Welcome to our team!",
                "expires_at": "2024-01-15T10:30:00Z",
                "tenant_branding": {
                    "logo_url": "https://example.com/logo.png",
                    "primary_color": "#007bff",
                    "secondary_color": "#6c757d"
                }
            }
        }
```

## 🧪 Testing Requirements
- [ ] Test invitation creation schema validation
- [ ] Test bulk invitation validation and limits
- [ ] Test invitation acceptance schema validation
- [ ] Test password validation rules
- [ ] Test email format validation
- [ ] Test role validation
- [ ] Test token validation
- [ ] Test metadata validation and security

## 📊 Validation Checklist
- [ ] All schemas have proper validation rules
- [ ] Email validation prevents invalid formats
- [ ] Password validation enforces security
- [ ] Role validation prevents invalid roles
- [ ] Token validation ensures security
- [ ] Error messages are clear and helpful
- [ ] Examples are provided for documentation

## 🚨 Security Considerations
- [ ] Password validation enforces strong passwords
- [ ] Metadata validation prevents sensitive data
- [ ] Token validation ensures security
- [ ] Email validation prevents injection
- [ ] Role validation prevents privilege escalation

## 📈 Performance Considerations
- [ ] Validation is fast and efficient
- [ ] Bulk operations have reasonable limits
- [ ] Serialization is optimized
- [ ] Memory usage is minimal

## 🎯 Definition of Done
- [ ] All invitation schemas are implemented
- [ ] Validation rules work correctly
- [ ] Error messages are clear and helpful
- [ ] Examples are provided for API documentation
- [ ] Security validation prevents vulnerabilities
- [ ] Performance requirements are met
- [ ] All tests pass
- [ ] Code review is completed
